#!/usr/bin/env python3
"""
Light or Dead - Complete 2D Action RPG
A comprehensive action RPG with all modern features

Features:
- Complete character system with HP/MP/XP and level progression
- Comprehensive item system with weapons, armor, gems, and flasks
- Drag-and-drop inventory with right-click context menus
- Passive skill tree system with multiple branches
- Real-time combat with various monster types and boss mechanics
- Economy system with gold currency and NPC merchants
- Transparent UI system with comprehensive controls
- Audio system with background music and sound effects
"""

import pygame
import sys
import math
import random
import json
import os
import time
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple, Any
import urllib.request
import threading

# Initialize Pygame
pygame.init()
pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

# === CONSTANTS ===
SCREEN_WIDTH = 1200
SCREEN_HEIGHT = 800
FPS = 60
GAME_TITLE = "Light or Dead"

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
CYAN = (0, 255, 255)
MAGENTA = (255, 0, 255)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
LIGHT_GRAY = (192, 192, 192)
TRANSPARENT = (0, 0, 0, 0)

# Item Rarities
class ItemRarity(Enum):
    COMMON = (WHITE, "Common", 1.0)
    RARE = (BLUE, "Rare", 1.5)
    EPIC = (PURPLE, "Epic", 2.0)
    LEGENDARY = (ORANGE, "Legendary", 3.0)

# Game States
class GameState(Enum):
    MAIN_MENU = "main_menu"
    PLAYING = "playing"
    INVENTORY = "inventory"
    SKILL_TREE = "skill_tree"
    SHOP = "shop"
    SETTINGS = "settings"
    PAUSED = "paused"
    GAME_OVER = "game_over"

# Player Constants
PLAYER_SIZE = 32
PLAYER_BASE_SPEED = 150
PLAYER_BASE_HP = 100
PLAYER_BASE_MP = 50
PLAYER_REGEN_HP = 1.0  # HP per second
PLAYER_REGEN_MP = 2.0  # MP per second

# Dash System
DASH_DISTANCE = 120
DASH_SPEED = 600
DASH_COOLDOWN = 2.0
DASH_DURATION = 0.2
DOUBLE_TAP_TIME = 0.3

# Combat Constants
BASE_DAMAGE = 10
CRIT_CHANCE = 0.05
CRIT_MULTIPLIER = 2.0

# Experience System
BASE_XP_REQUIREMENT = 100
XP_MULTIPLIER = 1.5

# === CORE CLASSES ===

@dataclass
class Vector2:
    """2D Vector class for position and movement"""
    x: float = 0.0
    y: float = 0.0
    
    def __add__(self, other):
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other):
        return Vector2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar):
        return Vector2(self.x * scalar, self.y * scalar)
    
    def length(self):
        return math.sqrt(self.x * self.x + self.y * self.y)
    
    def normalize(self):
        length = self.length()
        if length > 0:
            return Vector2(self.x / length, self.y / length)
        return Vector2(0, 0)
    
    def distance_to(self, other):
        return (self - other).length()

class GameObject:
    """Base class for all game objects"""
    def __init__(self, position: Vector2, size: int = 32):
        self.position = position
        self.size = size
        self.rect = pygame.Rect(position.x - size//2, position.y - size//2, size, size)
        self.active = True
    
    def update(self, dt: float):
        """Update object state"""
        self.rect.center = (int(self.position.x), int(self.position.y))
    
    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw object to surface"""
        pass
    
    def get_bounds(self):
        """Get collision bounds"""
        return self.rect

class AudioManager:
    """Manages all audio in the game"""
    def __init__(self):
        self.sounds = {}
        self.music_volume = 0.25  # 25% default as requested
        self.sfx_volume = 0.25
        self.master_volume = 0.25
        self.music_playing = False
        
        # Load default sounds
        self.load_default_sounds()
    
    def load_default_sounds(self):
        """Load default sound effects"""
        # Create simple sound effects programmatically if files don't exist
        try:
            # Try to load existing sounds first
            sound_files = {
                'hit': 'assets/sounds/Hit 1.wav',
                'shoot': 'assets/sounds/Shoot 1.wav',
                'pickup': 'assets/sounds/Coin 1.wav',
                'level_up': 'assets/sounds/Powerup 1.wav',
                'death': 'assets/sounds/Explosion 1.wav',
                'menu_select': 'assets/sounds/Select 1.wav',
                'shop_buy': 'assets/sounds/Coin 2.wav',
                'error': 'assets/sounds/Wrong 1.wav'
            }
            
            for name, file_path in sound_files.items():
                if os.path.exists(file_path):
                    self.sounds[name] = pygame.mixer.Sound(file_path)
                    self.sounds[name].set_volume(self.sfx_volume * self.master_volume)
        except Exception as e:
            print(f"Warning: Could not load some sound files: {e}")
    
    def play_sound(self, sound_name: str):
        """Play a sound effect"""
        if sound_name in self.sounds:
            self.sounds[sound_name].play()
    
    def load_background_music(self, url: str):
        """Load background music from URL"""
        def download_music():
            try:
                print("Downloading background music...")
                urllib.request.urlretrieve(url, "temp_music.mp3")
                pygame.mixer.music.load("temp_music.mp3")
                pygame.mixer.music.set_volume(self.music_volume * self.master_volume)
                pygame.mixer.music.play(-1)  # Loop indefinitely
                self.music_playing = True
                print("Background music loaded and playing")
            except Exception as e:
                print(f"Could not load background music: {e}")
        
        # Download in background thread
        threading.Thread(target=download_music, daemon=True).start()
    
    def set_volumes(self, master: float, music: float, sfx: float):
        """Set volume levels"""
        self.master_volume = master
        self.music_volume = music
        self.sfx_volume = sfx
        
        # Apply to music
        if self.music_playing:
            pygame.mixer.music.set_volume(self.music_volume * self.master_volume)
        
        # Apply to sound effects
        for sound in self.sounds.values():
            sound.set_volume(self.sfx_volume * self.master_volume)

# Global audio manager instance
audio_manager = AudioManager()

# === PLAYER CHARACTER SYSTEM ===

class PlayerStats:
    """Player character statistics"""
    def __init__(self):
        # Core Stats
        self.level = 1
        self.experience = 0
        self.experience_to_next = BASE_XP_REQUIREMENT

        # Health and Mana
        self.max_hp = PLAYER_BASE_HP
        self.current_hp = self.max_hp
        self.max_mp = PLAYER_BASE_MP
        self.current_mp = self.max_mp

        # Primary Attributes
        self.strength = 10
        self.defense = 10
        self.speed = 10
        self.intelligence = 10
        self.dexterity = 10

        # Derived Stats
        self.damage = BASE_DAMAGE
        self.crit_chance = CRIT_CHANCE
        self.crit_multiplier = CRIT_MULTIPLIER
        self.movement_speed = PLAYER_BASE_SPEED

        # Skill Points
        self.skill_points = 0
        self.skill_points_spent = 0

    def add_experience(self, amount: int):
        """Add experience and handle level ups"""
        self.experience += amount

        while self.experience >= self.experience_to_next:
            self.level_up()

    def level_up(self):
        """Level up the player"""
        self.experience -= self.experience_to_next
        self.level += 1
        self.skill_points += 1

        # Increase base stats
        self.max_hp += 5
        self.max_mp += 3
        self.current_hp = self.max_hp  # Full heal on level up
        self.current_mp = self.max_mp

        # Calculate next level requirement
        self.experience_to_next = int(BASE_XP_REQUIREMENT * (XP_MULTIPLIER ** (self.level - 1)))

        audio_manager.play_sound('level_up')
        print(f"Level up! Now level {self.level}")

    def regenerate(self, dt: float):
        """Regenerate HP and MP over time"""
        if self.current_hp < self.max_hp:
            self.current_hp = min(self.max_hp, self.current_hp + PLAYER_REGEN_HP * dt)

        if self.current_mp < self.max_mp:
            self.current_mp = min(self.max_mp, self.current_mp + PLAYER_REGEN_MP * dt)

class Player(GameObject):
    """Player character class"""
    def __init__(self, position: Vector2):
        super().__init__(position, PLAYER_SIZE)
        self.stats = PlayerStats()
        self.velocity = Vector2(0, 0)

        # Dash system
        self.dash_cooldown_timer = 0.0
        self.dash_timer = 0.0
        self.is_dashing = False
        self.dash_direction = Vector2(0, 0)

        # Double-tap detection for dash
        self.last_space_press = 0.0
        self.space_press_count = 0

        # Movement state
        self.moving = False
        self.facing_direction = Vector2(1, 0)

        # Combat
        self.last_attack_time = 0.0
        self.attack_cooldown = 0.5

        # Status effects
        self.invulnerable = False
        self.invulnerable_timer = 0.0

    def update(self, dt: float):
        """Update player state"""
        super().update(dt)

        # Update timers
        if self.dash_cooldown_timer > 0:
            self.dash_cooldown_timer -= dt

        if self.dash_timer > 0:
            self.dash_timer -= dt
            if self.dash_timer <= 0:
                self.is_dashing = False

        if self.invulnerable_timer > 0:
            self.invulnerable_timer -= dt
            if self.invulnerable_timer <= 0:
                self.invulnerable = False

        # Handle dash movement
        if self.is_dashing:
            self.position = self.position + self.dash_direction * DASH_SPEED * dt
        else:
            # Normal movement
            self.position = self.position + self.velocity * dt

        # Keep player on screen
        self.position.x = max(PLAYER_SIZE//2, min(SCREEN_WIDTH - PLAYER_SIZE//2, self.position.x))
        self.position.y = max(PLAYER_SIZE//2, min(SCREEN_HEIGHT - PLAYER_SIZE//2, self.position.y))

        # Regenerate stats
        self.stats.regenerate(dt)

        # Update facing direction based on movement
        if self.velocity.length() > 0:
            self.facing_direction = self.velocity.normalize()
            self.moving = True
        else:
            self.moving = False

    def handle_input(self, keys, dt: float):
        """Handle player input"""
        if self.is_dashing:
            return  # Can't control during dash

        # Movement input
        movement = Vector2(0, 0)
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            movement.y -= 1
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            movement.y += 1
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            movement.x -= 1
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            movement.x += 1

        # Normalize diagonal movement
        if movement.length() > 0:
            movement = movement.normalize()
            self.velocity = movement * self.stats.movement_speed
        else:
            self.velocity = Vector2(0, 0)

        # Dash input (double-tap space)
        current_time = time.time()
        if keys[pygame.K_SPACE]:
            if current_time - self.last_space_press < DOUBLE_TAP_TIME:
                self.space_press_count += 1
                if self.space_press_count >= 2:
                    self.attempt_dash()
                    self.space_press_count = 0
            else:
                self.space_press_count = 1
            self.last_space_press = current_time

    def attempt_dash(self):
        """Attempt to perform a dash"""
        if self.dash_cooldown_timer <= 0 and not self.is_dashing:
            # Use current facing direction or movement direction
            dash_dir = self.facing_direction
            if self.velocity.length() > 0:
                dash_dir = self.velocity.normalize()

            self.dash_direction = dash_dir
            self.is_dashing = True
            self.dash_timer = DASH_DURATION
            self.dash_cooldown_timer = DASH_COOLDOWN
            self.invulnerable = True
            self.invulnerable_timer = DASH_DURATION

            audio_manager.play_sound('dash')
            print("Dash!")

    def take_damage(self, amount: int, damage_type: str = "physical"):
        """Take damage with defense calculation"""
        if self.invulnerable:
            return False

        # Calculate damage reduction from defense
        damage_reduction = self.stats.defense * 0.5
        actual_damage = max(1, amount - damage_reduction)

        self.stats.current_hp -= actual_damage

        # Brief invulnerability after taking damage
        self.invulnerable = True
        self.invulnerable_timer = 0.5

        audio_manager.play_sound('hit')

        if self.stats.current_hp <= 0:
            self.stats.current_hp = 0
            return True  # Player died

        return False

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw player character"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Draw player as colored circle (will be replaced with sprite later)
        color = WHITE
        if self.invulnerable:
            # Flash when invulnerable
            flash_time = int(time.time() * 10) % 2
            color = WHITE if flash_time else LIGHT_GRAY

        pygame.draw.circle(surface, color, draw_pos, PLAYER_SIZE//2)

        # Draw direction indicator
        end_pos = (draw_pos[0] + int(self.facing_direction.x * PLAYER_SIZE//2),
                  draw_pos[1] + int(self.facing_direction.y * PLAYER_SIZE//2))
        pygame.draw.line(surface, RED, draw_pos, end_pos, 2)

# === ITEM SYSTEM ===

class ItemType(Enum):
    WEAPON = "weapon"
    ARMOR = "armor"
    GEM = "gem"
    FLASK = "flask"
    CONSUMABLE = "consumable"

class WeaponType(Enum):
    SWORD = ("Sword", 1.0, 15, 25)
    AXE = ("Axe", 0.8, 20, 35)
    BOW = ("Bow", 1.2, 10, 20)
    STAFF = ("Staff", 0.9, 12, 22)
    DAGGER = ("Dagger", 1.5, 8, 15)

class ArmorType(Enum):
    HELMET = "Helmet"
    CHEST = "Chest Armor"
    GLOVES = "Gloves"
    BOOTS = "Boots"
    BELT = "Belt"
    RING = "Ring"
    AMULET = "Amulet"

class GemType(Enum):
    DAMAGE = ("Damage Gem", "Increases weapon damage")
    SPEED = ("Speed Gem", "Increases attack/movement speed")
    DEFENSE = ("Defense Gem", "Increases armor and resistances")
    LIFE = ("Life Gem", "Increases maximum health")
    MANA = ("Mana Gem", "Increases maximum mana")

class FlaskType(Enum):
    HEALTH = ("Health Flask", "Restores health over time")
    MANA = ("Mana Flask", "Restores mana over time")
    HYBRID = ("Hybrid Flask", "Restores both health and mana")

class Item:
    """Base item class"""
    def __init__(self, name: str, item_type: ItemType, rarity: ItemRarity, level: int = 1):
        self.name = name
        self.item_type = item_type
        self.rarity = rarity
        self.level = level
        self.stats = {}
        self.description = ""
        self.value = 10  # Base gold value
        self.stack_size = 1
        self.current_stack = 1

        # Generate random stats based on rarity
        self.generate_stats()

    def generate_stats(self):
        """Generate random stats based on item type and rarity"""
        rarity_multiplier = self.rarity.value[2]

        if self.item_type == ItemType.WEAPON:
            self.stats['damage'] = int(random.randint(5, 15) * rarity_multiplier)
            if random.random() < 0.3:  # 30% chance for crit
                self.stats['crit_chance'] = random.randint(1, 5) * rarity_multiplier

        elif self.item_type == ItemType.ARMOR:
            self.stats['defense'] = int(random.randint(3, 10) * rarity_multiplier)
            if random.random() < 0.4:  # 40% chance for health
                self.stats['max_health'] = int(random.randint(10, 25) * rarity_multiplier)

        elif self.item_type == ItemType.GEM:
            gem_effects = ['damage', 'speed', 'defense', 'max_health', 'max_mana']
            effect = random.choice(gem_effects)
            self.stats[effect] = int(random.randint(2, 8) * rarity_multiplier)

        # Update value based on stats
        self.value = int(self.value * rarity_multiplier * (1 + len(self.stats) * 0.5))

    def get_tooltip_text(self) -> List[str]:
        """Get tooltip text for the item"""
        lines = []
        lines.append(f"{self.name}")
        lines.append(f"{self.rarity.value[1]} {self.item_type.value.title()}")
        lines.append("")

        # Add stats
        for stat, value in self.stats.items():
            if stat == 'damage':
                lines.append(f"+{value} Damage")
            elif stat == 'defense':
                lines.append(f"+{value} Defense")
            elif stat == 'crit_chance':
                lines.append(f"+{value}% Critical Chance")
            elif stat == 'max_health':
                lines.append(f"+{value} Maximum Health")
            elif stat == 'max_mana':
                lines.append(f"+{value} Maximum Mana")
            elif stat == 'speed':
                lines.append(f"+{value}% Speed")

        lines.append("")
        lines.append(f"Value: {self.value} gold")

        return lines

class Weapon(Item):
    """Weapon item class"""
    def __init__(self, weapon_type: WeaponType, rarity: ItemRarity, level: int = 1):
        self.weapon_type = weapon_type
        name = f"{rarity.value[1]} {weapon_type.value[0]}"
        super().__init__(name, ItemType.WEAPON, rarity, level)

        # Weapon-specific stats
        self.attack_speed = weapon_type.value[1]
        self.min_damage = weapon_type.value[2]
        self.max_damage = weapon_type.value[3]

        # Override damage stat with weapon-specific calculation
        base_damage = random.randint(self.min_damage, self.max_damage)
        self.stats['damage'] = int(base_damage * rarity.value[2])

class Armor(Item):
    """Armor item class"""
    def __init__(self, armor_type: ArmorType, rarity: ItemRarity, level: int = 1):
        self.armor_type = armor_type
        name = f"{rarity.value[1]} {armor_type.value}"
        super().__init__(name, ItemType.ARMOR, rarity, level)

class Gem(Item):
    """Gem/Skill item class"""
    def __init__(self, gem_type: GemType, rarity: ItemRarity, level: int = 1):
        self.gem_type = gem_type
        name = f"{rarity.value[1]} {gem_type.value[0]}"
        super().__init__(name, ItemType.GEM, rarity, level)
        self.description = gem_type.value[1]

class Flask(Item):
    """Flask item class"""
    def __init__(self, flask_type: FlaskType, rarity: ItemRarity, level: int = 1):
        self.flask_type = flask_type
        name = f"{rarity.value[1]} {flask_type.value[0]}"
        super().__init__(name, ItemType.FLASK, rarity, level)
        self.description = flask_type.value[1]
        self.stack_size = 5
        self.current_stack = random.randint(1, 3)

        # Flask-specific stats
        if flask_type == FlaskType.HEALTH:
            self.stats['heal_amount'] = int(random.randint(20, 50) * rarity.value[2])
        elif flask_type == FlaskType.MANA:
            self.stats['mana_amount'] = int(random.randint(15, 40) * rarity.value[2])
        elif flask_type == FlaskType.HYBRID:
            self.stats['heal_amount'] = int(random.randint(15, 35) * rarity.value[2])
            self.stats['mana_amount'] = int(random.randint(10, 30) * rarity.value[2])

class ItemGenerator:
    """Generates random items"""
    @staticmethod
    def generate_random_item(player_level: int = 1) -> Item:
        """Generate a random item appropriate for player level"""
        # Determine rarity based on random chance
        rarity_roll = random.random()
        if rarity_roll < 0.6:
            rarity = ItemRarity.COMMON
        elif rarity_roll < 0.85:
            rarity = ItemRarity.RARE
        elif rarity_roll < 0.97:
            rarity = ItemRarity.EPIC
        else:
            rarity = ItemRarity.LEGENDARY

        # Determine item type
        item_type_roll = random.random()
        if item_type_roll < 0.3:
            # Weapon
            weapon_type = random.choice(list(WeaponType))
            return Weapon(weapon_type, rarity, player_level)
        elif item_type_roll < 0.6:
            # Armor
            armor_type = random.choice(list(ArmorType))
            return Armor(armor_type, rarity, player_level)
        elif item_type_roll < 0.8:
            # Gem
            gem_type = random.choice(list(GemType))
            return Gem(gem_type, rarity, player_level)
        else:
            # Flask
            flask_type = random.choice(list(FlaskType))
            return Flask(flask_type, rarity, player_level)

# === INVENTORY SYSTEM ===

class InventorySlot:
    """Represents a single inventory slot"""
    def __init__(self, x: int, y: int, size: int = 48):
        self.rect = pygame.Rect(x, y, size, size)
        self.item = None
        self.highlighted = False
        self.selected = False

    def can_accept_item(self, item: Item) -> bool:
        """Check if this slot can accept the given item"""
        return self.item is None or (
            self.item.name == item.name and
            self.item.current_stack + item.current_stack <= self.item.stack_size
        )

    def add_item(self, item: Item) -> bool:
        """Add item to this slot"""
        if self.item is None:
            self.item = item
            return True
        elif self.item.name == item.name and self.item.current_stack + item.current_stack <= self.item.stack_size:
            self.item.current_stack += item.current_stack
            return True
        return False

    def remove_item(self) -> Optional[Item]:
        """Remove and return the item from this slot"""
        item = self.item
        self.item = None
        return item

    def draw(self, surface: pygame.Surface):
        """Draw the inventory slot"""
        # Draw slot background
        color = LIGHT_GRAY if self.highlighted else DARK_GRAY
        if self.selected:
            color = YELLOW

        pygame.draw.rect(surface, color, self.rect)
        pygame.draw.rect(surface, WHITE, self.rect, 2)

        # Draw item if present
        if self.item:
            # Draw item as colored rectangle (will be replaced with sprites later)
            item_color = self.item.rarity.value[0]
            item_rect = pygame.Rect(self.rect.x + 4, self.rect.y + 4,
                                  self.rect.width - 8, self.rect.height - 8)
            pygame.draw.rect(surface, item_color, item_rect)

            # Draw stack count if > 1
            if self.item.current_stack > 1:
                font = pygame.font.Font(None, 20)
                stack_text = font.render(str(self.item.current_stack), True, WHITE)
                surface.blit(stack_text, (self.rect.right - 20, self.rect.bottom - 20))

class Inventory:
    """Player inventory system"""
    def __init__(self, width: int = 8, height: int = 8):
        self.width = width
        self.height = height
        self.slots = []
        self.gold = 0

        # Equipment slots
        self.equipment_slots = {
            'weapon': None,
            'helmet': None,
            'chest': None,
            'gloves': None,
            'boots': None,
            'belt': None,
            'ring1': None,
            'ring2': None,
            'amulet': None
        }

        # UI state
        self.visible = False
        self.dragging_item = None
        self.drag_offset = Vector2(0, 0)
        self.tooltip_item = None
        self.context_menu_visible = False
        self.context_menu_pos = (0, 0)
        self.context_menu_item = None

        # Create inventory slots
        self.create_slots()

    def create_slots(self):
        """Create the inventory slot grid"""
        slot_size = 48
        padding = 4
        start_x = 50
        start_y = 100

        for row in range(self.height):
            for col in range(self.width):
                x = start_x + col * (slot_size + padding)
                y = start_y + row * (slot_size + padding)
                slot = InventorySlot(x, y, slot_size)
                self.slots.append(slot)

    def add_item(self, item: Item) -> bool:
        """Add item to inventory"""
        # Try to stack with existing items first
        for slot in self.slots:
            if slot.can_accept_item(item) and slot.item is not None:
                if slot.add_item(item):
                    return True

        # Find empty slot
        for slot in self.slots:
            if slot.item is None:
                slot.add_item(item)
                return True

        return False  # Inventory full

    def remove_item(self, item: Item) -> bool:
        """Remove item from inventory"""
        for slot in self.slots:
            if slot.item == item:
                slot.remove_item()
                return True
        return False

    def get_slot_at_pos(self, pos: Tuple[int, int]) -> Optional[InventorySlot]:
        """Get inventory slot at mouse position"""
        for slot in self.slots:
            if slot.rect.collidepoint(pos):
                return slot
        return None

    def handle_mouse_event(self, event):
        """Handle mouse events for inventory"""
        if not self.visible:
            return

        mouse_pos = pygame.mouse.get_pos()

        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.handle_left_click(mouse_pos)
            elif event.button == 3:  # Right click
                self.handle_right_click(mouse_pos)

        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1 and self.dragging_item:
                self.handle_drop(mouse_pos)

        elif event.type == pygame.MOUSEMOTION:
            self.handle_mouse_move(mouse_pos)

    def handle_left_click(self, pos: Tuple[int, int]):
        """Handle left mouse click"""
        slot = self.get_slot_at_pos(pos)
        if slot and slot.item:
            # Start dragging
            self.dragging_item = slot.item
            self.drag_offset = Vector2(pos[0] - slot.rect.centerx, pos[1] - slot.rect.centery)
            slot.remove_item()

    def handle_right_click(self, pos: Tuple[int, int]):
        """Handle right mouse click"""
        slot = self.get_slot_at_pos(pos)
        if slot and slot.item:
            # Show context menu
            self.context_menu_visible = True
            self.context_menu_pos = pos
            self.context_menu_item = slot.item

    def handle_drop(self, pos: Tuple[int, int]):
        """Handle item drop"""
        if not self.dragging_item:
            return

        target_slot = self.get_slot_at_pos(pos)
        if target_slot and target_slot.can_accept_item(self.dragging_item):
            target_slot.add_item(self.dragging_item)
        else:
            # Return to original position or find empty slot
            self.add_item(self.dragging_item)

        self.dragging_item = None

    def handle_mouse_move(self, pos: Tuple[int, int]):
        """Handle mouse movement"""
        # Update slot highlighting
        for slot in self.slots:
            slot.highlighted = slot.rect.collidepoint(pos)

        # Update tooltip
        slot = self.get_slot_at_pos(pos)
        self.tooltip_item = slot.item if slot else None

    def use_item(self, item: Item, player: Player):
        """Use/consume an item"""
        if item.item_type == ItemType.FLASK:
            # Use flask
            if 'heal_amount' in item.stats:
                player.stats.current_hp = min(player.stats.max_hp,
                                            player.stats.current_hp + item.stats['heal_amount'])
            if 'mana_amount' in item.stats:
                player.stats.current_mp = min(player.stats.max_mp,
                                            player.stats.current_mp + item.stats['mana_amount'])

            # Reduce stack or remove item
            item.current_stack -= 1
            if item.current_stack <= 0:
                self.remove_item(item)

            audio_manager.play_sound('pickup')

    def draw(self, surface: pygame.Surface):
        """Draw the inventory"""
        if not self.visible:
            return

        # Draw background
        bg_rect = pygame.Rect(30, 80, 450, 450)
        pygame.draw.rect(surface, (0, 0, 0, 180), bg_rect)
        pygame.draw.rect(surface, WHITE, bg_rect, 2)

        # Draw title
        font = pygame.font.Font(None, 36)
        title_text = font.render("Inventory", True, WHITE)
        surface.blit(title_text, (50, 50))

        # Draw gold
        gold_text = font.render(f"Gold: {self.gold}", True, YELLOW)
        surface.blit(gold_text, (300, 50))

        # Draw slots
        for slot in self.slots:
            slot.draw(surface)

        # Draw dragging item
        if self.dragging_item:
            mouse_pos = pygame.mouse.get_pos()
            drag_rect = pygame.Rect(mouse_pos[0] - 24, mouse_pos[1] - 24, 48, 48)
            item_color = self.dragging_item.rarity.value[0]
            pygame.draw.rect(surface, item_color, drag_rect)
            pygame.draw.rect(surface, WHITE, drag_rect, 2)

        # Draw tooltip
        if self.tooltip_item and not self.dragging_item:
            self.draw_tooltip(surface, pygame.mouse.get_pos())

        # Draw context menu
        if self.context_menu_visible:
            self.draw_context_menu(surface)

    def draw_tooltip(self, surface: pygame.Surface, pos: Tuple[int, int]):
        """Draw item tooltip"""
        if not self.tooltip_item:
            return

        lines = self.tooltip_item.get_tooltip_text()
        font = pygame.font.Font(None, 24)

        # Calculate tooltip size
        max_width = 0
        line_height = 25
        for line in lines:
            text_width = font.size(line)[0]
            max_width = max(max_width, text_width)

        tooltip_width = max_width + 20
        tooltip_height = len(lines) * line_height + 10

        # Position tooltip
        tooltip_x = pos[0] + 10
        tooltip_y = pos[1] - tooltip_height - 10

        # Keep tooltip on screen
        if tooltip_x + tooltip_width > SCREEN_WIDTH:
            tooltip_x = pos[0] - tooltip_width - 10
        if tooltip_y < 0:
            tooltip_y = pos[1] + 10

        # Draw tooltip background
        tooltip_rect = pygame.Rect(tooltip_x, tooltip_y, tooltip_width, tooltip_height)
        pygame.draw.rect(surface, (0, 0, 0, 200), tooltip_rect)
        pygame.draw.rect(surface, self.tooltip_item.rarity.value[0], tooltip_rect, 2)

        # Draw tooltip text
        y_offset = tooltip_y + 5
        for line in lines:
            if line:  # Skip empty lines
                text_surface = font.render(line, True, WHITE)
                surface.blit(text_surface, (tooltip_x + 10, y_offset))
            y_offset += line_height

    def draw_context_menu(self, surface: pygame.Surface):
        """Draw right-click context menu"""
        if not self.context_menu_item:
            return

        menu_items = ["Use", "Drop", "Cancel"]
        font = pygame.font.Font(None, 24)

        menu_width = 100
        menu_height = len(menu_items) * 30
        menu_x = self.context_menu_pos[0]
        menu_y = self.context_menu_pos[1]

        # Draw menu background
        menu_rect = pygame.Rect(menu_x, menu_y, menu_width, menu_height)
        pygame.draw.rect(surface, (40, 40, 40), menu_rect)
        pygame.draw.rect(surface, WHITE, menu_rect, 2)

        # Draw menu items
        for i, item_text in enumerate(menu_items):
            y = menu_y + i * 30 + 5
            text_surface = font.render(item_text, True, WHITE)
            surface.blit(text_surface, (menu_x + 10, y))

# === SKILL TREE SYSTEM ===

class SkillType(Enum):
    COMBAT = "Combat"
    DEFENSE = "Defense"
    UTILITY = "Utility"

class SkillNode:
    """Represents a single skill in the skill tree"""
    def __init__(self, name: str, description: str, skill_type: SkillType,
                 position: Vector2, max_level: int = 5, prerequisites: List[str] = None):
        self.name = name
        self.description = description
        self.skill_type = skill_type
        self.position = position
        self.max_level = max_level
        self.current_level = 0
        self.prerequisites = prerequisites or []
        self.connections = []  # Connected skill nodes

        # Visual properties
        self.radius = 25
        self.rect = pygame.Rect(position.x - self.radius, position.y - self.radius,
                               self.radius * 2, self.radius * 2)
        self.hovered = False
        self.available = False

    def can_level_up(self, skill_tree) -> bool:
        """Check if this skill can be leveled up"""
        if self.current_level >= self.max_level:
            return False

        # Check prerequisites
        for prereq_name in self.prerequisites:
            prereq_skill = skill_tree.get_skill(prereq_name)
            if not prereq_skill or prereq_skill.current_level == 0:
                return False

        return True

    def level_up(self, player_stats: PlayerStats) -> bool:
        """Level up this skill and apply bonuses"""
        if self.current_level >= self.max_level:
            return False

        self.current_level += 1
        player_stats.skill_points_spent += 1

        # Apply skill bonuses based on skill name
        self.apply_bonus(player_stats)

        audio_manager.play_sound('level_up')
        return True

    def apply_bonus(self, player_stats: PlayerStats):
        """Apply skill bonus to player stats"""
        if self.name == "Increased Damage":
            player_stats.damage += 2
        elif self.name == "Increased Health":
            player_stats.max_hp += 10
            player_stats.current_hp += 10
        elif self.name == "Increased Mana":
            player_stats.max_mp += 5
            player_stats.current_mp += 5
        elif self.name == "Increased Speed":
            player_stats.movement_speed += 10
        elif self.name == "Increased Defense":
            player_stats.defense += 3
        elif self.name == "Critical Strike":
            player_stats.crit_chance += 0.02  # +2% per level
        elif self.name == "Critical Multiplier":
            player_stats.crit_multiplier += 0.1
        elif self.name == "Strength":
            player_stats.strength += 2
        elif self.name == "Dexterity":
            player_stats.dexterity += 2
        elif self.name == "Intelligence":
            player_stats.intelligence += 2

    def get_color(self) -> Tuple[int, int, int]:
        """Get skill node color based on state"""
        if self.current_level > 0:
            return YELLOW  # Learned
        elif self.available:
            return GREEN   # Available to learn
        else:
            return GRAY    # Not available

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw the skill node"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Draw connections first (behind the node)
        for connected_skill in self.connections:
            connected_pos = (int(connected_skill.position.x - camera_offset.x),
                           int(connected_skill.position.y - camera_offset.y))

            # Color connection based on both nodes' states
            if self.current_level > 0 and connected_skill.current_level > 0:
                line_color = YELLOW
            elif self.current_level > 0 or connected_skill.current_level > 0:
                line_color = LIGHT_GRAY
            else:
                line_color = DARK_GRAY

            pygame.draw.line(surface, line_color, draw_pos, connected_pos, 2)

        # Draw skill node
        color = self.get_color()
        if self.hovered:
            pygame.draw.circle(surface, WHITE, draw_pos, self.radius + 3, 3)

        pygame.draw.circle(surface, color, draw_pos, self.radius)
        pygame.draw.circle(surface, WHITE, draw_pos, self.radius, 2)

        # Draw skill level
        if self.current_level > 0:
            font = pygame.font.Font(None, 24)
            level_text = font.render(str(self.current_level), True, BLACK)
            text_rect = level_text.get_rect(center=draw_pos)
            surface.blit(level_text, text_rect)

        # Draw skill type icon (simple colored dot)
        type_colors = {
            SkillType.COMBAT: RED,
            SkillType.DEFENSE: BLUE,
            SkillType.UTILITY: GREEN
        }
        type_color = type_colors.get(self.skill_type, WHITE)
        type_pos = (draw_pos[0] + self.radius - 8, draw_pos[1] - self.radius + 8)
        pygame.draw.circle(surface, type_color, type_pos, 5)

class SkillTree:
    """Manages the passive skill tree"""
    def __init__(self):
        self.skills = {}
        self.visible = False
        self.camera_offset = Vector2(0, 0)
        self.selected_skill = None

        # Create skill tree
        self.create_skill_tree()

    def create_skill_tree(self):
        """Create the skill tree structure"""
        # Combat branch (left side)
        combat_skills = [
            SkillNode("Increased Damage", "Increases base damage by 2", SkillType.COMBAT,
                     Vector2(200, 300)),
            SkillNode("Critical Strike", "Increases critical chance by 2%", SkillType.COMBAT,
                     Vector2(200, 200), prerequisites=["Increased Damage"]),
            SkillNode("Critical Multiplier", "Increases critical multiplier by 0.1", SkillType.COMBAT,
                     Vector2(200, 100), prerequisites=["Critical Strike"]),
            SkillNode("Strength", "Increases strength by 2", SkillType.COMBAT,
                     Vector2(100, 250), prerequisites=["Increased Damage"])
        ]

        # Defense branch (center)
        defense_skills = [
            SkillNode("Increased Health", "Increases maximum health by 10", SkillType.DEFENSE,
                     Vector2(400, 350)),
            SkillNode("Increased Defense", "Increases defense by 3", SkillType.DEFENSE,
                     Vector2(400, 250), prerequisites=["Increased Health"]),
            SkillNode("Increased Mana", "Increases maximum mana by 5", SkillType.DEFENSE,
                     Vector2(400, 150), prerequisites=["Increased Defense"])
        ]

        # Utility branch (right side)
        utility_skills = [
            SkillNode("Increased Speed", "Increases movement speed by 10", SkillType.UTILITY,
                     Vector2(600, 300)),
            SkillNode("Dexterity", "Increases dexterity by 2", SkillType.UTILITY,
                     Vector2(700, 250), prerequisites=["Increased Speed"]),
            SkillNode("Intelligence", "Increases intelligence by 2", SkillType.UTILITY,
                     Vector2(600, 200), prerequisites=["Increased Speed"])
        ]

        # Add all skills to the tree
        all_skills = combat_skills + defense_skills + utility_skills
        for skill in all_skills:
            self.skills[skill.name] = skill

        # Set up connections
        self.setup_connections()

        # Update availability
        self.update_availability()

    def setup_connections(self):
        """Set up visual connections between skills"""
        connections = [
            ("Increased Damage", "Critical Strike"),
            ("Critical Strike", "Critical Multiplier"),
            ("Increased Damage", "Strength"),
            ("Increased Health", "Increased Defense"),
            ("Increased Defense", "Increased Mana"),
            ("Increased Speed", "Dexterity"),
            ("Increased Speed", "Intelligence"),
            # Cross-branch connections
            ("Increased Damage", "Increased Health"),
            ("Increased Health", "Increased Speed")
        ]

        for skill1_name, skill2_name in connections:
            skill1 = self.skills.get(skill1_name)
            skill2 = self.skills.get(skill2_name)
            if skill1 and skill2:
                skill1.connections.append(skill2)
                skill2.connections.append(skill1)

    def get_skill(self, name: str) -> Optional[SkillNode]:
        """Get skill by name"""
        return self.skills.get(name)

    def update_availability(self):
        """Update which skills are available to learn"""
        for skill in self.skills.values():
            skill.available = skill.can_level_up(self)

        # Starting skills are always available if not maxed
        starting_skills = ["Increased Damage", "Increased Health", "Increased Speed"]
        for skill_name in starting_skills:
            skill = self.skills.get(skill_name)
            if skill and skill.current_level < skill.max_level:
                skill.available = True

    def handle_mouse_event(self, event, player_stats: PlayerStats):
        """Handle mouse events for skill tree"""
        if not self.visible:
            return

        mouse_pos = pygame.mouse.get_pos()
        world_pos = Vector2(mouse_pos[0] + self.camera_offset.x,
                           mouse_pos[1] + self.camera_offset.y)

        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                self.handle_skill_click(world_pos, player_stats)

        elif event.type == pygame.MOUSEMOTION:
            self.handle_mouse_move(world_pos)

    def handle_skill_click(self, world_pos: Vector2, player_stats: PlayerStats):
        """Handle clicking on a skill"""
        for skill in self.skills.values():
            distance = skill.position.distance_to(world_pos)
            if distance <= skill.radius:
                if skill.available and player_stats.skill_points > 0:
                    if skill.level_up(player_stats):
                        player_stats.skill_points -= 1
                        self.update_availability()
                        print(f"Learned {skill.name} (Level {skill.current_level})")
                break

    def handle_mouse_move(self, world_pos: Vector2):
        """Handle mouse movement over skills"""
        self.selected_skill = None
        for skill in self.skills.values():
            distance = skill.position.distance_to(world_pos)
            skill.hovered = distance <= skill.radius
            if skill.hovered:
                self.selected_skill = skill

    def draw(self, surface: pygame.Surface):
        """Draw the skill tree"""
        if not self.visible:
            return

        # Draw background
        bg_rect = pygame.Rect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)
        pygame.draw.rect(surface, (0, 0, 0, 180), bg_rect)

        # Draw title
        font = pygame.font.Font(None, 48)
        title_text = font.render("Skill Tree", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 50))
        surface.blit(title_text, title_rect)

        # Draw all skills
        for skill in self.skills.values():
            skill.draw(surface, self.camera_offset)

        # Draw skill tooltip
        if self.selected_skill:
            self.draw_skill_tooltip(surface)

    def draw_skill_tooltip(self, surface: pygame.Surface):
        """Draw tooltip for selected skill"""
        if not self.selected_skill:
            return

        skill = self.selected_skill
        mouse_pos = pygame.mouse.get_pos()

        # Prepare tooltip text
        lines = [
            skill.name,
            f"Type: {skill.skill_type.value}",
            f"Level: {skill.current_level}/{skill.max_level}",
            "",
            skill.description
        ]

        if skill.prerequisites:
            lines.append("")
            lines.append("Prerequisites:")
            for prereq in skill.prerequisites:
                prereq_skill = self.get_skill(prereq)
                status = "✓" if prereq_skill and prereq_skill.current_level > 0 else "✗"
                lines.append(f"  {status} {prereq}")

        # Draw tooltip
        font = pygame.font.Font(None, 24)
        max_width = max(font.size(line)[0] for line in lines if line)
        tooltip_width = max_width + 20
        tooltip_height = len(lines) * 25 + 10

        tooltip_x = mouse_pos[0] + 10
        tooltip_y = mouse_pos[1] - tooltip_height - 10

        # Keep tooltip on screen
        if tooltip_x + tooltip_width > SCREEN_WIDTH:
            tooltip_x = mouse_pos[0] - tooltip_width - 10
        if tooltip_y < 0:
            tooltip_y = mouse_pos[1] + 10

        # Draw tooltip background
        tooltip_rect = pygame.Rect(tooltip_x, tooltip_y, tooltip_width, tooltip_height)
        pygame.draw.rect(surface, (0, 0, 0, 200), tooltip_rect)
        pygame.draw.rect(surface, skill.get_color(), tooltip_rect, 2)

        # Draw tooltip text
        y_offset = tooltip_y + 5
        for line in lines:
            if line:
                text_surface = font.render(line, True, WHITE)
                surface.blit(text_surface, (tooltip_x + 10, y_offset))
            y_offset += 25

# === COMBAT SYSTEM ===

class Projectile(GameObject):
    """Base projectile class"""
    def __init__(self, position: Vector2, direction: Vector2, speed: float, damage: int,
                 size: int = 8, color: Tuple[int, int, int] = YELLOW, lifetime: float = 3.0):
        super().__init__(position, size)
        self.direction = direction.normalize()
        self.speed = speed
        self.damage = damage
        self.color = color
        self.lifetime = lifetime
        self.age = 0.0

    def update(self, dt: float):
        """Update projectile"""
        super().update(dt)

        # Move projectile
        self.position = self.position + self.direction * self.speed * dt

        # Age projectile
        self.age += dt
        if self.age >= self.lifetime:
            self.active = False

        # Remove if off screen
        if (self.position.x < -50 or self.position.x > SCREEN_WIDTH + 50 or
            self.position.y < -50 or self.position.y > SCREEN_HEIGHT + 50):
            self.active = False

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw projectile"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))
        pygame.draw.circle(surface, self.color, draw_pos, self.size//2)

class Grenade(GameObject):
    """Grenade projectile that explodes after a timer"""
    def __init__(self, position: Vector2, target_pos: Vector2, damage: int = 30,
                 explosion_radius: int = 80, fuse_time: float = 2.5):
        super().__init__(position, 12)
        self.target_pos = target_pos
        self.damage = damage
        self.explosion_radius = explosion_radius
        self.fuse_time = fuse_time
        self.fuse_timer = fuse_time
        self.exploded = False

        # Calculate arc trajectory
        distance = position.distance_to(target_pos)
        self.velocity = (target_pos - position) * (1.0 / fuse_time)
        self.velocity.y -= 100  # Add upward arc

    def update(self, dt: float):
        """Update grenade"""
        super().update(dt)

        if not self.exploded:
            # Move grenade
            self.position = self.position + self.velocity * dt
            self.velocity.y += 200 * dt  # Gravity

            # Update fuse timer
            self.fuse_timer -= dt
            if self.fuse_timer <= 0:
                self.explode()

    def explode(self):
        """Explode the grenade"""
        self.exploded = True
        self.active = False
        audio_manager.play_sound('explosion')
        print(f"Grenade exploded at {self.position.x}, {self.position.y}")

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw grenade"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Flash red when about to explode
        color = RED if self.fuse_timer < 0.5 else ORANGE
        pygame.draw.circle(surface, color, draw_pos, self.size//2)
        pygame.draw.circle(surface, WHITE, draw_pos, self.size//2, 2)

class EnemyType(Enum):
    BASIC = ("Basic Enemy", 30, 50, 10, GREEN)
    FAST = ("Fast Enemy", 20, 80, 8, CYAN)
    TANK = ("Tank Enemy", 60, 30, 15, ORANGE)
    SHOOTER = ("Shooter Enemy", 25, 40, 12, PURPLE)

class Enemy(GameObject):
    """Base enemy class"""
    def __init__(self, position: Vector2, enemy_type: EnemyType, level: int = 1):
        super().__init__(position, 24)
        self.enemy_type = enemy_type
        self.level = level

        # Scale stats with level
        level_multiplier = 1.0 + (level - 1) * 0.2
        self.max_health = int(enemy_type.value[1] * level_multiplier)
        self.current_health = self.max_health
        self.speed = enemy_type.value[2]
        self.damage = int(enemy_type.value[3] * level_multiplier)
        self.color = enemy_type.value[4]

        # AI state
        self.target = None
        self.last_attack_time = 0.0
        self.attack_cooldown = 1.5
        self.attack_range = 40
        self.sight_range = 200

        # Movement
        self.velocity = Vector2(0, 0)
        self.path_update_timer = 0.0

        # Experience reward
        self.xp_reward = int(10 * level_multiplier)

    def update(self, dt: float, player: Player):
        """Update enemy AI and movement"""
        super().update(dt)

        # Update timers
        if self.last_attack_time > 0:
            self.last_attack_time -= dt

        self.path_update_timer -= dt

        # Find target (player)
        if player and self.position.distance_to(player.position) <= self.sight_range:
            self.target = player

        # AI behavior
        if self.target:
            self.update_ai(dt)

        # Move
        self.position = self.position + self.velocity * dt

        # Keep on screen
        self.position.x = max(self.size//2, min(SCREEN_WIDTH - self.size//2, self.position.x))
        self.position.y = max(self.size//2, min(SCREEN_HEIGHT - self.size//2, self.position.y))

    def update_ai(self, dt: float):
        """Update AI behavior"""
        if not self.target:
            return

        distance_to_target = self.position.distance_to(self.target.position)

        # Different behavior based on enemy type
        if self.enemy_type == EnemyType.BASIC or self.enemy_type == EnemyType.FAST or self.enemy_type == EnemyType.TANK:
            # Melee enemies - move towards player
            if distance_to_target > self.attack_range:
                direction = (self.target.position - self.position).normalize()
                self.velocity = direction * self.speed
            else:
                self.velocity = Vector2(0, 0)
                # Attack if cooldown is ready
                if self.last_attack_time <= 0:
                    self.attack_target()

        elif self.enemy_type == EnemyType.SHOOTER:
            # Ranged enemy - keep distance and shoot
            if distance_to_target < 100:
                # Move away from player
                direction = (self.position - self.target.position).normalize()
                self.velocity = direction * self.speed * 0.5
            elif distance_to_target > 150:
                # Move closer to player
                direction = (self.target.position - self.position).normalize()
                self.velocity = direction * self.speed * 0.3
            else:
                self.velocity = Vector2(0, 0)

            # Shoot at player
            if self.last_attack_time <= 0:
                self.shoot_at_target()

    def attack_target(self):
        """Attack the target (melee)"""
        if not self.target:
            return

        distance = self.position.distance_to(self.target.position)
        if distance <= self.attack_range:
            # Deal damage to player
            if self.target.take_damage(self.damage):
                print("Player died!")

            self.last_attack_time = self.attack_cooldown
            audio_manager.play_sound('hit')

    def shoot_at_target(self):
        """Shoot projectile at target"""
        if not self.target:
            return

        direction = (self.target.position - self.position).normalize()
        projectile = Projectile(self.position, direction, 200, self.damage//2,
                              color=RED, lifetime=2.0)

        self.last_attack_time = self.attack_cooldown
        audio_manager.play_sound('shoot')

        return projectile  # Return projectile to be added to game world

    def take_damage(self, damage: int) -> bool:
        """Take damage and return True if enemy died"""
        self.current_health -= damage

        if self.current_health <= 0:
            self.current_health = 0
            self.active = False
            audio_manager.play_sound('death')
            return True

        return False

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw enemy"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Draw enemy body
        pygame.draw.circle(surface, self.color, draw_pos, self.size//2)
        pygame.draw.circle(surface, WHITE, draw_pos, self.size//2, 2)

        # Draw health bar
        if self.current_health < self.max_health:
            bar_width = self.size
            bar_height = 6
            bar_x = draw_pos[0] - bar_width//2
            bar_y = draw_pos[1] - self.size//2 - 10

            # Background
            health_bg = pygame.Rect(bar_x, bar_y, bar_width, bar_height)
            pygame.draw.rect(surface, RED, health_bg)

            # Health
            health_percent = self.current_health / self.max_health
            health_width = int(bar_width * health_percent)
            health_rect = pygame.Rect(bar_x, bar_y, health_width, bar_height)
            pygame.draw.rect(surface, GREEN, health_rect)

class BossType(Enum):
    SPLITTER = ("Splitter Boss", 150, 40, 25)
    GRENADIER = ("Grenadier Boss", 120, 35, 20)
    SUMMONER = ("Summoner Boss", 100, 45, 18)

class Boss(Enemy):
    """Boss enemy with special abilities"""
    def __init__(self, position: Vector2, boss_type: BossType, level: int = 1):
        # Initialize as enemy first
        super().__init__(position, EnemyType.TANK, level)

        # Override with boss stats
        self.boss_type = boss_type
        self.size = 48  # Larger than normal enemies

        level_multiplier = 1.0 + (level - 1) * 0.3
        self.max_health = int(boss_type.value[1] * level_multiplier)
        self.current_health = self.max_health
        self.speed = boss_type.value[2]
        self.damage = int(boss_type.value[3] * level_multiplier)
        self.color = MAGENTA

        # Boss-specific properties
        self.special_ability_cooldown = 5.0
        self.special_ability_timer = 0.0
        self.has_split = False  # For splitter boss

        # Higher XP reward
        self.xp_reward = int(100 * level_multiplier)

    def update(self, dt: float, player: Player):
        """Update boss with special abilities"""
        super().update(dt, player)

        # Update special ability timer
        if self.special_ability_timer > 0:
            self.special_ability_timer -= dt

        # Use special ability
        if self.special_ability_timer <= 0 and self.target:
            self.use_special_ability()
            self.special_ability_timer = self.special_ability_cooldown

    def use_special_ability(self):
        """Use boss special ability"""
        if self.boss_type == BossType.SPLITTER:
            self.split_ability()
        elif self.boss_type == BossType.GRENADIER:
            self.grenade_ability()
        elif self.boss_type == BossType.SUMMONER:
            self.summon_ability()

    def split_ability(self):
        """Split into smaller enemies when health is low"""
        if self.current_health <= self.max_health * 0.5 and not self.has_split:
            self.has_split = True
            print("Boss is splitting!")
            # This would create smaller enemies - handled by game world
            return True
        return False

    def grenade_ability(self):
        """Throw grenades at player's feet"""
        if not self.target:
            return None

        # Throw grenade at player's current position
        grenade = Grenade(self.position, self.target.position,
                         damage=self.damage, explosion_radius=80)
        print("Boss threw a grenade!")
        return grenade

    def summon_ability(self):
        """Summon minion enemies"""
        print("Boss is summoning minions!")
        # This would create new enemies - handled by game world
        return True

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw boss with special effects"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Draw boss body with pulsing effect
        pulse = int(10 * math.sin(time.time() * 5))
        boss_radius = self.size//2 + pulse
        pygame.draw.circle(surface, self.color, draw_pos, boss_radius)
        pygame.draw.circle(surface, WHITE, draw_pos, boss_radius, 3)

        # Draw boss type indicator
        font = pygame.font.Font(None, 20)
        type_text = font.render(self.boss_type.value[0], True, WHITE)
        text_rect = type_text.get_rect(center=(draw_pos[0], draw_pos[1] - boss_radius - 20))
        surface.blit(type_text, text_rect)

        # Draw health bar (larger for boss)
        bar_width = self.size * 2
        bar_height = 8
        bar_x = draw_pos[0] - bar_width//2
        bar_y = draw_pos[1] - boss_radius - 15

        # Background
        health_bg = pygame.Rect(bar_x, bar_y, bar_width, bar_height)
        pygame.draw.rect(surface, RED, health_bg)

        # Health
        health_percent = self.current_health / self.max_health
        health_width = int(bar_width * health_percent)
        health_rect = pygame.Rect(bar_x, bar_y, health_width, bar_height)
        pygame.draw.rect(surface, GREEN, health_rect)

# === ECONOMY AND SHOP SYSTEM ===

class ShopItem:
    """Represents an item in the shop"""
    def __init__(self, item: Item, price: int, stock: int = 1):
        self.item = item
        self.price = price
        self.stock = stock
        self.original_price = price

class Merchant:
    """NPC Merchant class"""
    def __init__(self, name: str, position: Vector2):
        self.name = name
        self.position = position
        self.inventory = []
        self.gold = 1000
        self.buy_price_multiplier = 0.6  # Buys items at 60% of value
        self.sell_price_multiplier = 1.2  # Sells items at 120% of value

        # Generate initial shop inventory
        self.restock_inventory()

    def restock_inventory(self):
        """Restock merchant inventory with random items"""
        self.inventory.clear()

        # Add some basic items
        for _ in range(8):
            item = ItemGenerator.generate_random_item(1)
            price = int(item.value * self.sell_price_multiplier)
            stock = random.randint(1, 3) if item.item_type == ItemType.FLASK else 1
            shop_item = ShopItem(item, price, stock)
            self.inventory.append(shop_item)

        # Add some guaranteed useful items
        health_flask = Flask(FlaskType.HEALTH, ItemRarity.COMMON)
        self.inventory.append(ShopItem(health_flask, 50, 5))

        mana_flask = Flask(FlaskType.MANA, ItemRarity.COMMON)
        self.inventory.append(ShopItem(mana_flask, 40, 5))

    def can_buy_item(self, item: Item, player_gold: int) -> bool:
        """Check if player can buy item from merchant"""
        shop_item = self.find_shop_item(item)
        if not shop_item:
            return False

        return player_gold >= shop_item.price and shop_item.stock > 0

    def buy_item_from_player(self, item: Item, player_inventory: Inventory) -> bool:
        """Buy item from player"""
        buy_price = int(item.value * self.buy_price_multiplier)

        if self.gold >= buy_price:
            self.gold -= buy_price
            player_inventory.gold += buy_price
            player_inventory.remove_item(item)
            audio_manager.play_sound('shop_buy')
            return True

        return False

    def sell_item_to_player(self, item: Item, player_inventory: Inventory) -> bool:
        """Sell item to player"""
        shop_item = self.find_shop_item(item)
        if not shop_item:
            return False

        if player_inventory.gold >= shop_item.price and shop_item.stock > 0:
            if player_inventory.add_item(item):
                player_inventory.gold -= shop_item.price
                self.gold += shop_item.price
                shop_item.stock -= 1

                # Remove item if out of stock
                if shop_item.stock <= 0:
                    self.inventory.remove(shop_item)

                audio_manager.play_sound('shop_buy')
                return True

        return False

    def find_shop_item(self, item: Item) -> Optional[ShopItem]:
        """Find shop item by item reference"""
        for shop_item in self.inventory:
            if shop_item.item == item:
                return shop_item
        return None

    def get_sell_price(self, item: Item) -> int:
        """Get price merchant will pay for item"""
        return int(item.value * self.buy_price_multiplier)

class Shop:
    """Shop interface"""
    def __init__(self):
        self.visible = False
        self.merchant = None
        self.selected_item = None
        self.shop_mode = "buy"  # "buy" or "sell"

        # UI elements
        self.buy_button_rect = pygame.Rect(50, 50, 100, 40)
        self.sell_button_rect = pygame.Rect(160, 50, 100, 40)
        self.close_button_rect = pygame.Rect(SCREEN_WIDTH - 100, 20, 80, 30)

        # Item display
        self.item_slots = []
        self.create_item_slots()

    def create_item_slots(self):
        """Create item display slots"""
        slot_size = 64
        padding = 10
        start_x = 50
        start_y = 120
        cols = 6

        for i in range(12):  # 2 rows of 6 items
            row = i // cols
            col = i % cols
            x = start_x + col * (slot_size + padding)
            y = start_y + row * (slot_size + padding)
            rect = pygame.Rect(x, y, slot_size, slot_size)
            self.item_slots.append(rect)

    def open_shop(self, merchant: Merchant):
        """Open shop with given merchant"""
        self.merchant = merchant
        self.visible = True
        self.shop_mode = "buy"
        self.selected_item = None

    def close_shop(self):
        """Close shop"""
        self.visible = False
        self.merchant = None
        self.selected_item = None

    def handle_mouse_event(self, event, player_inventory: Inventory):
        """Handle mouse events for shop"""
        if not self.visible or not self.merchant:
            return

        mouse_pos = pygame.mouse.get_pos()

        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                # Check UI buttons
                if self.buy_button_rect.collidepoint(mouse_pos):
                    self.shop_mode = "buy"
                    audio_manager.play_sound('menu_select')
                elif self.sell_button_rect.collidepoint(mouse_pos):
                    self.shop_mode = "sell"
                    audio_manager.play_sound('menu_select')
                elif self.close_button_rect.collidepoint(mouse_pos):
                    self.close_shop()
                    audio_manager.play_sound('menu_select')
                else:
                    # Check item slots
                    self.handle_item_click(mouse_pos, player_inventory)

    def handle_item_click(self, mouse_pos: Tuple[int, int], player_inventory: Inventory):
        """Handle clicking on items"""
        if self.shop_mode == "buy":
            # Buying from merchant
            for i, slot_rect in enumerate(self.item_slots):
                if slot_rect.collidepoint(mouse_pos) and i < len(self.merchant.inventory):
                    shop_item = self.merchant.inventory[i]
                    if self.merchant.can_buy_item(shop_item.item, player_inventory.gold):
                        if self.merchant.sell_item_to_player(shop_item.item, player_inventory):
                            print(f"Bought {shop_item.item.name} for {shop_item.price} gold")
                        else:
                            print("Cannot buy item - inventory full")
                            audio_manager.play_sound('error')
                    else:
                        print("Not enough gold or item out of stock")
                        audio_manager.play_sound('error')
                    break

        else:  # sell mode
            # Selling to merchant
            for i, slot_rect in enumerate(self.item_slots):
                if slot_rect.collidepoint(mouse_pos):
                    # Find corresponding inventory slot
                    player_slot_index = i
                    if player_slot_index < len(player_inventory.slots):
                        slot = player_inventory.slots[player_slot_index]
                        if slot.item:
                            sell_price = self.merchant.get_sell_price(slot.item)
                            if self.merchant.buy_item_from_player(slot.item, player_inventory):
                                print(f"Sold {slot.item.name} for {sell_price} gold")
                            else:
                                print("Merchant cannot afford this item")
                                audio_manager.play_sound('error')
                    break

    def draw(self, surface: pygame.Surface, player_inventory: Inventory):
        """Draw shop interface"""
        if not self.visible or not self.merchant:
            return

        # Draw background
        bg_rect = pygame.Rect(20, 20, SCREEN_WIDTH - 40, SCREEN_HEIGHT - 40)
        pygame.draw.rect(surface, (0, 0, 0, 200), bg_rect)
        pygame.draw.rect(surface, WHITE, bg_rect, 3)

        # Draw title
        font_large = pygame.font.Font(None, 48)
        title_text = font_large.render(f"{self.merchant.name}'s Shop", True, WHITE)
        surface.blit(title_text, (50, 20))

        # Draw mode buttons
        buy_color = YELLOW if self.shop_mode == "buy" else GRAY
        sell_color = YELLOW if self.shop_mode == "sell" else GRAY

        pygame.draw.rect(surface, buy_color, self.buy_button_rect)
        pygame.draw.rect(surface, WHITE, self.buy_button_rect, 2)

        pygame.draw.rect(surface, sell_color, self.sell_button_rect)
        pygame.draw.rect(surface, WHITE, self.sell_button_rect, 2)

        font_medium = pygame.font.Font(None, 32)
        buy_text = font_medium.render("Buy", True, BLACK)
        sell_text = font_medium.render("Sell", True, BLACK)

        buy_text_rect = buy_text.get_rect(center=self.buy_button_rect.center)
        sell_text_rect = sell_text.get_rect(center=self.sell_button_rect.center)

        surface.blit(buy_text, buy_text_rect)
        surface.blit(sell_text, sell_text_rect)

        # Draw close button
        pygame.draw.rect(surface, RED, self.close_button_rect)
        pygame.draw.rect(surface, WHITE, self.close_button_rect, 2)
        close_text = font_medium.render("Close", True, WHITE)
        close_text_rect = close_text.get_rect(center=self.close_button_rect.center)
        surface.blit(close_text, close_text_rect)

        # Draw player gold
        gold_text = font_medium.render(f"Gold: {player_inventory.gold}", True, YELLOW)
        surface.blit(gold_text, (SCREEN_WIDTH - 200, 60))

        # Draw items based on mode
        if self.shop_mode == "buy":
            self.draw_merchant_items(surface)
        else:
            self.draw_player_items(surface, player_inventory)

    def draw_merchant_items(self, surface: pygame.Surface):
        """Draw merchant's items for buying"""
        font_small = pygame.font.Font(None, 20)

        for i, shop_item in enumerate(self.merchant.inventory):
            if i >= len(self.item_slots):
                break

            slot_rect = self.item_slots[i]

            # Draw slot background
            pygame.draw.rect(surface, DARK_GRAY, slot_rect)
            pygame.draw.rect(surface, WHITE, slot_rect, 2)

            # Draw item
            item_color = shop_item.item.rarity.value[0]
            item_rect = pygame.Rect(slot_rect.x + 4, slot_rect.y + 4,
                                  slot_rect.width - 8, slot_rect.height - 8)
            pygame.draw.rect(surface, item_color, item_rect)

            # Draw price
            price_text = font_small.render(f"{shop_item.price}g", True, WHITE)
            surface.blit(price_text, (slot_rect.x + 2, slot_rect.bottom - 20))

            # Draw stock
            if shop_item.stock > 1:
                stock_text = font_small.render(f"x{shop_item.stock}", True, WHITE)
                surface.blit(stock_text, (slot_rect.right - 25, slot_rect.y + 2))

    def draw_player_items(self, surface: pygame.Surface, player_inventory: Inventory):
        """Draw player's items for selling"""
        font_small = pygame.font.Font(None, 20)

        for i, slot in enumerate(player_inventory.slots):
            if i >= len(self.item_slots):
                break

            slot_rect = self.item_slots[i]

            # Draw slot background
            pygame.draw.rect(surface, DARK_GRAY, slot_rect)
            pygame.draw.rect(surface, WHITE, slot_rect, 2)

            # Draw item if present
            if slot.item:
                item_color = slot.item.rarity.value[0]
                item_rect = pygame.Rect(slot_rect.x + 4, slot_rect.y + 4,
                                      slot_rect.width - 8, slot_rect.height - 8)
                pygame.draw.rect(surface, item_color, item_rect)

                # Draw sell price
                sell_price = self.merchant.get_sell_price(slot.item)
                price_text = font_small.render(f"{sell_price}g", True, WHITE)
                surface.blit(price_text, (slot_rect.x + 2, slot_rect.bottom - 20))

                # Draw stack count
                if slot.item.current_stack > 1:
                    stack_text = font_small.render(f"x{slot.item.current_stack}", True, WHITE)
                    surface.blit(stack_text, (slot_rect.right - 25, slot_rect.y + 2))

# === USER INTERFACE SYSTEM ===

class UI:
    """Main UI system"""
    def __init__(self):
        self.fonts = {
            'small': pygame.font.Font(None, 20),
            'medium': pygame.font.Font(None, 32),
            'large': pygame.font.Font(None, 48),
            'title': pygame.font.Font(None, 72)
        }

        # UI state
        self.show_debug = False
        self.death_message = ""
        self.death_timer = 0.0

        # Settings
        self.settings_visible = False
        self.settings_tabs = ["Audio", "Graphics", "Controls"]
        self.current_tab = 0

    def draw_hud(self, surface: pygame.Surface, player: Player):
        """Draw the main game HUD"""
        # Health bar
        self.draw_health_bar(surface, player.stats, 20, 20)

        # Mana bar
        self.draw_mana_bar(surface, player.stats, 20, 60)

        # Experience bar
        self.draw_experience_bar(surface, player.stats, 20, SCREEN_HEIGHT - 40)

        # Level display
        level_text = self.fonts['medium'].render(f"Level {player.stats.level}", True, WHITE)
        surface.blit(level_text, (20, 100))

        # Skill points
        if player.stats.skill_points > 0:
            sp_text = self.fonts['medium'].render(f"Skill Points: {player.stats.skill_points}", True, YELLOW)
            surface.blit(sp_text, (20, 130))

        # Dash cooldown indicator
        if player.dash_cooldown_timer > 0:
            cooldown_text = self.fonts['small'].render(f"Dash: {player.dash_cooldown_timer:.1f}s", True, CYAN)
            surface.blit(cooldown_text, (SCREEN_WIDTH - 150, 20))

        # Controls help
        self.draw_controls_help(surface)

    def draw_health_bar(self, surface: pygame.Surface, stats: PlayerStats, x: int, y: int):
        """Draw health bar"""
        bar_width = 200
        bar_height = 20

        # Background
        bg_rect = pygame.Rect(x, y, bar_width, bar_height)
        pygame.draw.rect(surface, DARK_GRAY, bg_rect)

        # Health
        health_percent = stats.current_hp / stats.max_hp
        health_width = int(bar_width * health_percent)
        health_rect = pygame.Rect(x, y, health_width, bar_height)
        pygame.draw.rect(surface, RED, health_rect)

        # Border
        pygame.draw.rect(surface, WHITE, bg_rect, 2)

        # Text
        health_text = self.fonts['small'].render(f"HP: {int(stats.current_hp)}/{stats.max_hp}", True, WHITE)
        text_rect = health_text.get_rect(center=bg_rect.center)
        surface.blit(health_text, text_rect)

    def draw_mana_bar(self, surface: pygame.Surface, stats: PlayerStats, x: int, y: int):
        """Draw mana bar"""
        bar_width = 200
        bar_height = 20

        # Background
        bg_rect = pygame.Rect(x, y, bar_width, bar_height)
        pygame.draw.rect(surface, DARK_GRAY, bg_rect)

        # Mana
        mana_percent = stats.current_mp / stats.max_mp
        mana_width = int(bar_width * mana_percent)
        mana_rect = pygame.Rect(x, y, mana_width, bar_height)
        pygame.draw.rect(surface, BLUE, mana_rect)

        # Border
        pygame.draw.rect(surface, WHITE, bg_rect, 2)

        # Text
        mana_text = self.fonts['small'].render(f"MP: {int(stats.current_mp)}/{stats.max_mp}", True, WHITE)
        text_rect = mana_text.get_rect(center=bg_rect.center)
        surface.blit(mana_text, text_rect)

    def draw_experience_bar(self, surface: pygame.Surface, stats: PlayerStats, x: int, y: int):
        """Draw experience bar"""
        bar_width = SCREEN_WIDTH - 40
        bar_height = 15

        # Background
        bg_rect = pygame.Rect(x, y, bar_width, bar_height)
        pygame.draw.rect(surface, DARK_GRAY, bg_rect)

        # Experience
        xp_percent = stats.experience / stats.experience_to_next
        xp_width = int(bar_width * xp_percent)
        xp_rect = pygame.Rect(x, y, xp_width, bar_height)
        pygame.draw.rect(surface, YELLOW, xp_rect)

        # Border
        pygame.draw.rect(surface, WHITE, bg_rect, 2)

        # Text
        xp_text = self.fonts['small'].render(f"XP: {stats.experience}/{stats.experience_to_next}", True, WHITE)
        text_rect = xp_text.get_rect(center=bg_rect.center)
        surface.blit(xp_text, text_rect)

    def draw_controls_help(self, surface: pygame.Surface):
        """Draw controls help"""
        controls = [
            "WASD - Move",
            "Double Space - Dash",
            "I - Inventory",
            "T - Skill Tree",
            "P - Shop",
            "ESC - Pause"
        ]

        y_offset = SCREEN_HEIGHT - 200
        for i, control in enumerate(controls):
            text = self.fonts['small'].render(control, True, LIGHT_GRAY)
            surface.blit(text, (SCREEN_WIDTH - 150, y_offset + i * 20))

    def draw_death_screen(self, surface: pygame.Surface, death_message: str):
        """Draw death screen"""
        # Dark overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(180)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))

        # Death message
        death_text = self.fonts['large'].render("YOU DIED", True, RED)
        death_rect = death_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        surface.blit(death_text, death_rect)

        # Cause of death
        if death_message:
            cause_text = self.fonts['medium'].render(death_message, True, WHITE)
            cause_rect = cause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            surface.blit(cause_text, cause_rect)

        # Restart instruction
        restart_text = self.fonts['medium'].render("Press R to restart or ESC for menu", True, LIGHT_GRAY)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 50))
        surface.blit(restart_text, restart_rect)

    def draw_pause_menu(self, surface: pygame.Surface):
        """Draw pause menu"""
        # Semi-transparent overlay
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(150)
        overlay.fill(BLACK)
        surface.blit(overlay, (0, 0))

        # Pause text
        pause_text = self.fonts['large'].render("PAUSED", True, WHITE)
        pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        surface.blit(pause_text, pause_rect)

        # Menu options
        options = [
            "ESC - Resume",
            "I - Inventory",
            "T - Skill Tree",
            "S - Settings",
            "Q - Quit to Menu"
        ]

        for i, option in enumerate(options):
            option_text = self.fonts['medium'].render(option, True, WHITE)
            option_rect = option_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 20 + i * 40))
            surface.blit(option_text, option_rect)

    def draw_settings_menu(self, surface: pygame.Surface):
        """Draw settings menu"""
        if not self.settings_visible:
            return

        # Background
        bg_rect = pygame.Rect(100, 100, SCREEN_WIDTH - 200, SCREEN_HEIGHT - 200)
        pygame.draw.rect(surface, (0, 0, 0, 200), bg_rect)
        pygame.draw.rect(surface, WHITE, bg_rect, 3)

        # Title
        title_text = self.fonts['large'].render("Settings", True, WHITE)
        surface.blit(title_text, (120, 120))

        # Tabs
        tab_width = 120
        tab_height = 40
        for i, tab in enumerate(self.settings_tabs):
            tab_x = 120 + i * (tab_width + 10)
            tab_y = 170
            tab_rect = pygame.Rect(tab_x, tab_y, tab_width, tab_height)

            color = YELLOW if i == self.current_tab else GRAY
            pygame.draw.rect(surface, color, tab_rect)
            pygame.draw.rect(surface, WHITE, tab_rect, 2)

            tab_text = self.fonts['medium'].render(tab, True, BLACK)
            tab_text_rect = tab_text.get_rect(center=tab_rect.center)
            surface.blit(tab_text, tab_text_rect)

        # Tab content
        if self.current_tab == 0:  # Audio
            self.draw_audio_settings(surface, 120, 230)
        elif self.current_tab == 1:  # Graphics
            self.draw_graphics_settings(surface, 120, 230)
        elif self.current_tab == 2:  # Controls
            self.draw_controls_settings(surface, 120, 230)

        # Close button
        close_text = self.fonts['medium'].render("ESC - Close", True, WHITE)
        surface.blit(close_text, (SCREEN_WIDTH - 200, SCREEN_HEIGHT - 150))

    def draw_audio_settings(self, surface: pygame.Surface, x: int, y: int):
        """Draw audio settings"""
        settings = [
            f"Master Volume: {int(audio_manager.master_volume * 100)}%",
            f"Music Volume: {int(audio_manager.music_volume * 100)}%",
            f"SFX Volume: {int(audio_manager.sfx_volume * 100)}%"
        ]

        for i, setting in enumerate(settings):
            text = self.fonts['medium'].render(setting, True, WHITE)
            surface.blit(text, (x, y + i * 40))

    def draw_graphics_settings(self, surface: pygame.Surface, x: int, y: int):
        """Draw graphics settings"""
        settings = [
            f"Resolution: {SCREEN_WIDTH}x{SCREEN_HEIGHT}",
            f"FPS: {FPS}",
            "Fullscreen: Off"
        ]

        for i, setting in enumerate(settings):
            text = self.fonts['medium'].render(setting, True, WHITE)
            surface.blit(text, (x, y + i * 40))

    def draw_controls_settings(self, surface: pygame.Surface, x: int, y: int):
        """Draw controls settings"""
        controls = [
            "Movement: WASD",
            "Dash: Double Space",
            "Inventory: I",
            "Skill Tree: T",
            "Shop: P",
            "Pause: ESC"
        ]

        for i, control in enumerate(controls):
            text = self.fonts['medium'].render(control, True, WHITE)
            surface.blit(text, (x, y + i * 30))

# === GAME WORLD ===

class GameWorld:
    """Manages the game world and all entities"""
    def __init__(self):
        self.player = Player(Vector2(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
        self.enemies = []
        self.projectiles = []
        self.grenades = []
        self.items = []

        # Systems
        self.inventory = Inventory()
        self.skill_tree = SkillTree()
        self.shop = Shop()
        self.ui = UI()

        # Merchants
        self.merchants = [
            Merchant("Weapon Smith", Vector2(100, 100)),
            Merchant("Potion Seller", Vector2(200, 100))
        ]

        # Game state
        self.wave = 1
        self.enemies_killed = 0
        self.wave_timer = 0.0
        self.wave_spawn_timer = 0.0
        self.enemies_to_spawn = 5

        # Camera
        self.camera_offset = Vector2(0, 0)

        # Give player some starting gold and items
        self.inventory.gold = 100
        for _ in range(3):
            item = ItemGenerator.generate_random_item(1)
            self.inventory.add_item(item)

    def update(self, dt: float):
        """Update game world"""
        # Update player
        keys = pygame.key.get_pressed()
        self.player.handle_input(keys, dt)
        self.player.update(dt)

        # Update enemies
        for enemy in self.enemies[:]:
            enemy.update(dt, self.player)
            if not enemy.active:
                # Enemy died, give XP and maybe drop item
                self.player.stats.add_experience(enemy.xp_reward)
                self.enemies_killed += 1

                # Chance to drop item
                if random.random() < 0.3:  # 30% drop chance
                    item = ItemGenerator.generate_random_item(self.player.stats.level)
                    item_obj = DroppedItem(enemy.position, item)
                    self.items.append(item_obj)

                self.enemies.remove(enemy)

        # Update projectiles
        for projectile in self.projectiles[:]:
            projectile.update(dt)
            if not projectile.active:
                self.projectiles.remove(projectile)
            else:
                # Check collision with player
                if projectile.position.distance_to(self.player.position) < PLAYER_SIZE//2 + projectile.size//2:
                    if self.player.take_damage(projectile.damage, "projectile"):
                        print("Player died from projectile!")
                    projectile.active = False

        # Update grenades
        for grenade in self.grenades[:]:
            grenade.update(dt)
            if grenade.exploded:
                # Check explosion damage to player
                distance = grenade.position.distance_to(self.player.position)
                if distance <= grenade.explosion_radius:
                    if self.player.take_damage(grenade.damage, "explosion"):
                        print("Player died from explosion!")
                self.grenades.remove(grenade)

        # Update items (pickup check)
        for item in self.items[:]:
            distance = item.position.distance_to(self.player.position)
            if distance < 40:  # Pickup range
                if self.inventory.add_item(item.item):
                    audio_manager.play_sound('pickup')
                    self.items.remove(item)

        # Wave management
        self.update_wave_system(dt)

        # Update camera (simple follow)
        self.camera_offset.x = self.player.position.x - SCREEN_WIDTH//2
        self.camera_offset.y = self.player.position.y - SCREEN_HEIGHT//2

    def update_wave_system(self, dt: float):
        """Update wave spawning system"""
        self.wave_timer += dt

        # Spawn enemies
        if len(self.enemies) < self.enemies_to_spawn and self.wave_spawn_timer <= 0:
            self.spawn_enemy()
            self.wave_spawn_timer = 2.0  # Spawn every 2 seconds
        else:
            self.wave_spawn_timer -= dt

        # Check for next wave
        if len(self.enemies) == 0 and self.enemies_killed >= self.enemies_to_spawn:
            self.start_next_wave()

    def spawn_enemy(self):
        """Spawn a random enemy"""
        # Random position at screen edge
        side = random.randint(0, 3)
        if side == 0:  # Top
            pos = Vector2(random.randint(0, SCREEN_WIDTH), -50)
        elif side == 1:  # Right
            pos = Vector2(SCREEN_WIDTH + 50, random.randint(0, SCREEN_HEIGHT))
        elif side == 2:  # Bottom
            pos = Vector2(random.randint(0, SCREEN_WIDTH), SCREEN_HEIGHT + 50)
        else:  # Left
            pos = Vector2(-50, random.randint(0, SCREEN_HEIGHT))

        # Choose enemy type based on wave
        if self.wave % 5 == 0:  # Boss wave
            boss_type = random.choice(list(BossType))
            enemy = Boss(pos, boss_type, self.wave // 5)
        else:
            enemy_type = random.choice(list(EnemyType))
            enemy = Enemy(pos, enemy_type, self.wave)

        self.enemies.append(enemy)

    def start_next_wave(self):
        """Start the next wave"""
        self.wave += 1
        self.enemies_to_spawn = 5 + self.wave * 2
        self.enemies_killed = 0
        self.wave_timer = 0.0
        print(f"Wave {self.wave} starting! Enemies to spawn: {self.enemies_to_spawn}")

    def draw(self, surface: pygame.Surface):
        """Draw the game world"""
        # Clear screen
        surface.fill(BLACK)

        # Draw world objects
        for item in self.items:
            item.draw(surface, self.camera_offset)

        for enemy in self.enemies:
            enemy.draw(surface, self.camera_offset)

        for projectile in self.projectiles:
            projectile.draw(surface, self.camera_offset)

        for grenade in self.grenades:
            grenade.draw(surface, self.camera_offset)

        # Draw player
        self.player.draw(surface, self.camera_offset)

        # Draw merchants (if near)
        for merchant in self.merchants:
            distance = merchant.position.distance_to(self.player.position)
            if distance < 100:
                draw_pos = (int(merchant.position.x - self.camera_offset.x),
                           int(merchant.position.y - self.camera_offset.y))
                pygame.draw.circle(surface, YELLOW, draw_pos, 20)

                # Draw name
                font = pygame.font.Font(None, 24)
                name_text = font.render(merchant.name, True, WHITE)
                name_rect = name_text.get_rect(center=(draw_pos[0], draw_pos[1] - 30))
                surface.blit(name_text, name_rect)

                if distance < 50:
                    interact_text = font.render("Press P to shop", True, WHITE)
                    interact_rect = interact_text.get_rect(center=(draw_pos[0], draw_pos[1] + 30))
                    surface.blit(interact_text, interact_rect)

        # Draw UI
        self.ui.draw_hud(surface, self.player)

        # Draw wave info
        wave_text = self.ui.fonts['medium'].render(f"Wave {self.wave}", True, WHITE)
        surface.blit(wave_text, (SCREEN_WIDTH//2 - 50, 20))

        enemies_text = self.ui.fonts['small'].render(f"Enemies: {len(self.enemies)}", True, WHITE)
        surface.blit(enemies_text, (SCREEN_WIDTH//2 - 50, 50))

class DroppedItem(GameObject):
    """Represents an item dropped in the world"""
    def __init__(self, position: Vector2, item: Item):
        super().__init__(position, 24)
        self.item = item
        self.bob_timer = 0.0
        self.original_y = position.y

    def update(self, dt: float):
        """Update dropped item (bobbing animation)"""
        super().update(dt)
        self.bob_timer += dt * 3
        self.position.y = self.original_y + math.sin(self.bob_timer) * 5

    def draw(self, surface: pygame.Surface, camera_offset: Vector2 = Vector2(0, 0)):
        """Draw dropped item"""
        draw_pos = (int(self.position.x - camera_offset.x),
                   int(self.position.y - camera_offset.y))

        # Draw item with rarity color
        color = self.item.rarity.value[0]
        pygame.draw.circle(surface, color, draw_pos, self.size//2)
        pygame.draw.circle(surface, WHITE, draw_pos, self.size//2, 2)

        # Draw glow effect for rare items
        if self.item.rarity != ItemRarity.COMMON:
            glow_radius = self.size//2 + int(5 * math.sin(self.bob_timer * 2))
            pygame.draw.circle(surface, (*color, 50), draw_pos, glow_radius, 2)

# === MAIN GAME ENGINE ===

class GameEngine:
    """Main game engine class"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption(GAME_TITLE)
        self.clock = pygame.time.Clock()
        self.running = True
        self.current_state = GameState.MAIN_MENU

        # Game world
        self.world = None

        # UI fonts
        self.fonts = {
            'small': pygame.font.Font(None, 24),
            'medium': pygame.font.Font(None, 36),
            'large': pygame.font.Font(None, 48),
            'title': pygame.font.Font(None, 72)
        }

        # Load background music
        music_url = "https://suno.com/s/NnO7RF4EIxHzhHFe"
        audio_manager.load_background_music(music_url)

        print("Light or Dead - Complete 2D Action RPG initialized!")
        print("Features: Character progression, Item system, Inventory, Skill tree, Combat, Economy")

    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds

            self.handle_events()
            self.update(dt)
            self.draw()

        pygame.quit()
        sys.exit()

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                self.handle_keydown(event.key)

            elif event.type == pygame.MOUSEBUTTONDOWN or event.type == pygame.MOUSEBUTTONUP or event.type == pygame.MOUSEMOTION:
                self.handle_mouse_event(event)

    def handle_keydown(self, key):
        """Handle key press events"""
        if key == pygame.K_ESCAPE:
            if self.current_state == GameState.PLAYING:
                self.current_state = GameState.PAUSED
            elif self.current_state == GameState.PAUSED:
                self.current_state = GameState.PLAYING
            elif self.current_state in [GameState.INVENTORY, GameState.SKILL_TREE, GameState.SHOP, GameState.SETTINGS]:
                self.current_state = GameState.PLAYING
            else:
                self.current_state = GameState.MAIN_MENU

        elif self.current_state == GameState.MAIN_MENU:
            if key == pygame.K_RETURN or key == pygame.K_SPACE:
                self.start_new_game()
            elif key == pygame.K_q:
                self.running = False

        elif self.current_state == GameState.PLAYING:
            if key == pygame.K_i:
                self.current_state = GameState.INVENTORY
                self.world.inventory.visible = True
            elif key == pygame.K_t:
                self.current_state = GameState.SKILL_TREE
                self.world.skill_tree.visible = True
            elif key == pygame.K_p:
                # Check if near merchant
                if self.world:
                    for merchant in self.world.merchants:
                        distance = merchant.position.distance_to(self.world.player.position)
                        if distance < 50:
                            self.current_state = GameState.SHOP
                            self.world.shop.open_shop(merchant)
                            break
            elif key == pygame.K_s:
                self.current_state = GameState.SETTINGS
                self.world.ui.settings_visible = True

        elif self.current_state == GameState.GAME_OVER:
            if key == pygame.K_r:
                self.start_new_game()
            elif key == pygame.K_ESCAPE:
                self.current_state = GameState.MAIN_MENU

        elif self.current_state == GameState.PAUSED:
            if key == pygame.K_i:
                self.current_state = GameState.INVENTORY
                self.world.inventory.visible = True
            elif key == pygame.K_t:
                self.current_state = GameState.SKILL_TREE
                self.world.skill_tree.visible = True
            elif key == pygame.K_s:
                self.current_state = GameState.SETTINGS
                self.world.ui.settings_visible = True
            elif key == pygame.K_q:
                self.current_state = GameState.MAIN_MENU

    def handle_mouse_event(self, event):
        """Handle mouse events"""
        if self.world:
            if self.current_state == GameState.INVENTORY:
                self.world.inventory.handle_mouse_event(event)
            elif self.current_state == GameState.SKILL_TREE:
                self.world.skill_tree.handle_mouse_event(event, self.world.player.stats)
            elif self.current_state == GameState.SHOP:
                self.world.shop.handle_mouse_event(event, self.world.inventory)

    def start_new_game(self):
        """Start a new game"""
        self.world = GameWorld()
        self.current_state = GameState.PLAYING
        audio_manager.play_sound('menu_select')
        print("New game started!")

    def update(self, dt: float):
        """Update game state"""
        if self.current_state == GameState.PLAYING and self.world:
            self.world.update(dt)

            # Check for player death
            if self.world.player.stats.current_hp <= 0:
                self.current_state = GameState.GAME_OVER
                self.world.ui.death_message = "You were overwhelmed by enemies"
                audio_manager.play_sound('death')

    def draw(self):
        """Draw everything to screen"""
        self.screen.fill(BLACK)

        if self.current_state == GameState.MAIN_MENU:
            self.draw_main_menu()

        elif self.current_state == GameState.PLAYING and self.world:
            self.world.draw(self.screen)

        elif self.current_state == GameState.PAUSED and self.world:
            self.world.draw(self.screen)
            self.world.ui.draw_pause_menu(self.screen)

        elif self.current_state == GameState.INVENTORY and self.world:
            self.world.draw(self.screen)
            self.world.inventory.draw(self.screen)

        elif self.current_state == GameState.SKILL_TREE and self.world:
            self.world.skill_tree.draw(self.screen)

        elif self.current_state == GameState.SHOP and self.world:
            self.world.draw(self.screen)
            self.world.shop.draw(self.screen, self.world.inventory)

        elif self.current_state == GameState.SETTINGS and self.world:
            self.world.draw(self.screen)
            self.world.ui.draw_settings_menu(self.screen)

        elif self.current_state == GameState.GAME_OVER and self.world:
            self.world.draw(self.screen)
            self.world.ui.draw_death_screen(self.screen, self.world.ui.death_message)

        pygame.display.flip()

    def draw_main_menu(self):
        """Draw main menu"""
        # Animated background
        for i in range(50):
            x = random.randint(0, SCREEN_WIDTH)
            y = random.randint(0, SCREEN_HEIGHT)
            size = random.randint(1, 3)
            alpha = random.randint(50, 150)
            color = (*WHITE, alpha)
            pygame.draw.circle(self.screen, WHITE, (x, y), size)

        # Title
        title_text = self.fonts['title'].render("LIGHT OR DEAD", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title_text, title_rect)

        # Subtitle
        subtitle_text = self.fonts['large'].render("Complete 2D Action RPG", True, LIGHT_GRAY)
        subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH//2, 280))
        self.screen.blit(subtitle_text, subtitle_rect)

        # Features list
        features = [
            "✓ Character progression with HP/MP/XP systems",
            "✓ Comprehensive item system with rarities",
            "✓ Drag-and-drop inventory management",
            "✓ Passive skill tree with multiple branches",
            "✓ Real-time combat with boss mechanics",
            "✓ Economy system with NPC merchants",
            "✓ Transparent UI with audio controls"
        ]

        for i, feature in enumerate(features):
            feature_text = self.fonts['small'].render(feature, True, GREEN)
            self.screen.blit(feature_text, (SCREEN_WIDTH//2 - 250, 350 + i * 25))

        # Instructions
        start_text = self.fonts['medium'].render("Press SPACE or ENTER to start", True, YELLOW)
        start_rect = start_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 100))
        self.screen.blit(start_text, start_rect)

        quit_text = self.fonts['medium'].render("Press Q to quit", True, LIGHT_GRAY)
        quit_rect = quit_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT - 60))
        self.screen.blit(quit_text, quit_rect)

# === MAIN ENTRY POINT ===

if __name__ == "__main__":
    try:
        game = GameEngine()
        game.run()
    except Exception as e:
        print(f"Game crashed with error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

class GameEngine:
    """Main game engine class"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption(GAME_TITLE)
        self.clock = pygame.time.Clock()
        self.running = True
        self.current_state = GameState.MAIN_MENU
        
        # Initialize font system
        self.fonts = {
            'small': pygame.font.Font(None, 24),
            'medium': pygame.font.Font(None, 36),
            'large': pygame.font.Font(None, 48),
            'title': pygame.font.Font(None, 72)
        }
        
        # Load background music
        music_url = "https://suno.com/s/NnO7RF4EIxHzhHFe"
        audio_manager.load_background_music(music_url)
        
        print("Game engine initialized successfully!")
    
    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds
            
            self.handle_events()
            self.update(dt)
            self.draw()
        
        pygame.quit()
        sys.exit()
    
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.current_state == GameState.PLAYING:
                        self.current_state = GameState.PAUSED
                    elif self.current_state == GameState.PAUSED:
                        self.current_state = GameState.PLAYING
                    else:
                        self.current_state = GameState.MAIN_MENU
    
    def update(self, dt: float):
        """Update game state"""
        pass
    
    def draw(self):
        """Draw everything to screen"""
        self.screen.fill(BLACK)
        
        # Draw based on current state
        if self.current_state == GameState.MAIN_MENU:
            self.draw_main_menu()
        
        pygame.display.flip()
    
    def draw_main_menu(self):
        """Draw main menu"""
        title_text = self.fonts['title'].render("LIGHT OR DEAD", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title_text, title_rect)
        
        subtitle_text = self.fonts['medium'].render("Complete 2D Action RPG", True, GRAY)
        subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH//2, 260))
        self.screen.blit(subtitle_text, subtitle_rect)

if __name__ == "__main__":
    game = GameEngine()
    game.run()
