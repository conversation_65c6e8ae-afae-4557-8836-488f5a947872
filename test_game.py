#!/usr/bin/env python3
"""
Quick test script to verify the game works correctly
"""

import subprocess
import sys
import time

def test_game_launch():
    """Test if the game launches without errors"""
    print("🧪 Testing game launch...")
    
    try:
        # Launch game for a few seconds
        process = subprocess.Popen([sys.executable, "farmer_replaced_clone.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # Wait a bit for initialization
        time.sleep(3)
        
        # Terminate the process
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        print("✅ Game launched successfully!")
        print("📦 Asset loading output:")
        print(stdout)
        
        if stderr:
            print("⚠️  Warnings/Errors:")
            print(stderr)
        
        return True
        
    except Exception as e:
        print(f"❌ Game launch failed: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import pygame
        print(f"✅ Pygame version: {pygame.version.ver}")
        
        import threading
        print("✅ Threading support available")
        
        import json
        print("✅ JSON support available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Install with: pip install pygame")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing 'The Farmer Was Replaced' Python Clone")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    print()
    
    # Test game launch
    if not test_game_launch():
        return False
    
    print()
    print("🎉 All tests passed!")
    print("🎮 Game is ready to play!")
    print()
    print("📋 How to play:")
    print("1. Run: python farmer_replaced_clone.py")
    print("2. Edit code in the code editor")
    print("3. Press F5 to execute your farming automation")
    print("4. Watch your drone work!")
    print()
    print("📚 Check example_farming_script.py for code examples")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
