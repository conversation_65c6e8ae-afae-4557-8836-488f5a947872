#!/usr/bin/env python3
"""
The Farmer Was Replaced - Python Clone
Inspired by the original game by <PERSON><PERSON>

A programming-based farming automation game where you write Python-like code
to control a drone that manages your farm automatically.
"""

import pygame
import sys
import json
import os
import time
import random
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Tuple
import threading
import queue

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1400
SCREEN_HEIGHT = 900
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
BROWN = (139, 69, 19)
BLUE = (0, 0, 255)
RED = (255, 0, 0)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
LIGHT_GRAY = (192, 192, 192)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)

# Game Constants
GRID_SIZE = 32
FARM_WIDTH = 10
FARM_HEIGHT = 10
OPERATION_TIME = 0.1  # Base time for operations

class Direction(Enum):
    NORTH = (0, -1)
    SOUTH = (0, 1)
    EAST = (1, 0)
    WEST = (-1, 0)

class GroundType(Enum):
    GRASSLAND = "Grassland"
    SOIL = "Soil"

class EntityType(Enum):
    NONE = None
    GRASS = "Grass"
    CARROT = "Carrot"
    PUMPKIN = "Pumpkin"
    SUNFLOWER = "Sunflower"
    TREE = "Tree"
    BUSH = "Bush"

class ItemType(Enum):
    CARROT = "Carrot"
    CARROT_SEED = "Carrot_Seed"
    PUMPKIN = "Pumpkin"
    PUMPKIN_SEED = "Pumpkin_Seed"
    SUNFLOWER_SEED = "Sunflower_Seed"
    WOOD = "Wood"
    HAY = "Hay"
    WATER_TANK = "Water_Tank"
    FERTILIZER = "Fertilizer"
    GOLD = "Gold"

class UnlockType(Enum):
    CARROTS = "Carrots"
    PUMPKINS = "Pumpkins"
    SUNFLOWERS = "Sunflowers"
    TREES = "Trees"
    SPEED = "Speed"
    EXPAND = "Expand"
    MULTI_TRADE = "Multi_Trade"
    FERTILIZER = "Fertilizer"
    WATERING = "Watering"

@dataclass
class FarmTile:
    ground_type: GroundType = GroundType.GRASSLAND
    entity_type: EntityType = EntityType.NONE
    entity_growth: float = 0.0  # 0.0 to 1.0
    water_level: float = 0.5  # 0.0 to 1.0
    
class GameState:
    def __init__(self):
        self.farm = [[FarmTile() for _ in range(FARM_WIDTH)] for _ in range(FARM_HEIGHT)]
        self.drone_x = 0
        self.drone_y = 0
        self.inventory = {item: 0 for item in ItemType}
        self.unlocks = {unlock: False for unlock in UnlockType}
        self.game_time = 0.0
        self.operation_speed = 1.0
        self.farm_size = 5  # Start with 5x5 farm
        
        # Starting resources
        self.inventory[ItemType.CARROT_SEED] = 10
        self.inventory[ItemType.GOLD] = 100
        
        # Starting unlocks
        self.unlocks[UnlockType.CARROTS] = True

class DroneAPI:
    """API that the user's code can call to control the drone"""
    
    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.output_log = []
        self.execution_time = 0.0
        
    def move(self, direction: Direction) -> bool:
        """Move the drone in the specified direction"""
        self._add_operation_time(200)
        
        dx, dy = direction.value
        new_x = (self.game_state.drone_x + dx) % self.game_state.farm_size
        new_y = (self.game_state.drone_y + dy) % self.game_state.farm_size
        
        self.game_state.drone_x = new_x
        self.game_state.drone_y = new_y
        return True
    
    def get_pos_x(self) -> int:
        """Get current X position"""
        self._add_operation_time(1)
        return self.game_state.drone_x
    
    def get_pos_y(self) -> int:
        """Get current Y position"""
        self._add_operation_time(1)
        return self.game_state.drone_y
    
    def get_ground_type(self) -> GroundType:
        """Get the ground type under the drone"""
        self._add_operation_time(1)
        tile = self._get_current_tile()
        return tile.ground_type
    
    def get_entity_type(self) -> Optional[EntityType]:
        """Get the entity type under the drone"""
        self._add_operation_time(1)
        tile = self._get_current_tile()
        return tile.entity_type if tile.entity_type != EntityType.NONE else None
    
    def can_harvest(self) -> bool:
        """Check if the entity under the drone can be harvested"""
        self._add_operation_time(1)
        tile = self._get_current_tile()
        return tile.entity_type != EntityType.NONE and tile.entity_growth >= 1.0
    
    def harvest(self) -> bool:
        """Harvest the entity under the drone"""
        tile = self._get_current_tile()
        
        if tile.entity_type == EntityType.NONE:
            self._add_operation_time(1)
            return False
        
        self._add_operation_time(200)
        
        # Give rewards based on entity type
        if tile.entity_type == EntityType.CARROT:
            self.game_state.inventory[ItemType.CARROT] += 1
        elif tile.entity_type == EntityType.PUMPKIN:
            self.game_state.inventory[ItemType.PUMPKIN] += 1
        elif tile.entity_type == EntityType.GRASS:
            self.game_state.inventory[ItemType.HAY] += 1
        elif tile.entity_type == EntityType.TREE:
            self.game_state.inventory[ItemType.WOOD] += random.randint(1, 3)
        
        # Remove the entity
        tile.entity_type = EntityType.NONE
        tile.entity_growth = 0.0
        return True
    
    def plant(self, entity: EntityType) -> bool:
        """Plant an entity under the drone"""
        tile = self._get_current_tile()
        
        if tile.entity_type != EntityType.NONE:
            self._add_operation_time(1)
            return False
        
        if tile.ground_type != GroundType.SOIL:
            self._add_operation_time(1)
            return False
        
        # Check if we have the required seed
        seed_required = None
        if entity == EntityType.CARROT:
            seed_required = ItemType.CARROT_SEED
        elif entity == EntityType.PUMPKIN:
            seed_required = ItemType.PUMPKIN_SEED
        elif entity == EntityType.SUNFLOWER:
            seed_required = ItemType.SUNFLOWER_SEED
        
        if seed_required and self.game_state.inventory[seed_required] <= 0:
            self._add_operation_time(1)
            return False
        
        self._add_operation_time(200)
        
        # Plant the entity
        tile.entity_type = entity
        tile.entity_growth = 0.0
        
        # Consume seed
        if seed_required:
            self.game_state.inventory[seed_required] -= 1
        
        return True
    
    def till(self):
        """Till the ground under the drone"""
        self._add_operation_time(200)
        tile = self._get_current_tile()
        
        if tile.ground_type == GroundType.GRASSLAND:
            tile.ground_type = GroundType.SOIL
        else:
            tile.ground_type = GroundType.GRASSLAND
    
    def num_items(self, item: ItemType) -> int:
        """Get the number of items in inventory"""
        self._add_operation_time(1)
        return self.game_state.inventory[item]
    
    def trade(self, item: ItemType, quantity: int = 1) -> bool:
        """Buy items from the market"""
        self._add_operation_time(200 if quantity == 1 else 200)
        
        # Simple trading costs
        costs = {
            ItemType.CARROT_SEED: 10,
            ItemType.PUMPKIN_SEED: 25,
            ItemType.SUNFLOWER_SEED: 15,
            ItemType.FERTILIZER: 50,
            ItemType.WATER_TANK: 30,
        }
        
        if item not in costs:
            return False
        
        total_cost = costs[item] * quantity
        if self.game_state.inventory[ItemType.GOLD] < total_cost:
            return False
        
        self.game_state.inventory[ItemType.GOLD] -= total_cost
        self.game_state.inventory[item] += quantity
        return True
    
    def quick_print(self, *args):
        """Print to the output log"""
        self._add_operation_time(1)
        message = " ".join(str(arg) for arg in args)
        self.output_log.append(f"[{self.execution_time:.1f}s] {message}")
    
    def get_time(self) -> float:
        """Get current execution time"""
        self._add_operation_time(1)
        return self.execution_time
    
    def _get_current_tile(self) -> FarmTile:
        """Get the tile under the drone"""
        return self.game_state.farm[self.game_state.drone_y][self.game_state.drone_x]
    
    def _add_operation_time(self, operations: int):
        """Add time for operations"""
        self.execution_time += (operations * OPERATION_TIME) / self.game_state.operation_speed

# Global variables for the API
North = Direction.NORTH
South = Direction.SOUTH
East = Direction.EAST
West = Direction.WEST

# Entity types for user code
Entities = EntityType
Items = ItemType
Grounds = GroundType
Unlocks = UnlockType

class CodeExecutor:
    """Executes user code in a controlled environment"""

    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.api = DroneAPI(game_state)
        self.is_running = False
        self.execution_thread = None
        self.stop_execution = False

    def execute_code(self, code: str):
        """Execute user code in a separate thread"""
        if self.is_running:
            self.stop()

        self.stop_execution = False
        self.api.output_log.clear()
        self.api.execution_time = 0.0

        self.execution_thread = threading.Thread(target=self._run_code, args=(code,))
        self.execution_thread.daemon = True
        self.is_running = True
        self.execution_thread.start()

    def stop(self):
        """Stop code execution"""
        self.stop_execution = True
        self.is_running = False
        if self.execution_thread:
            self.execution_thread.join(timeout=1.0)

    def _run_code(self, code: str):
        """Run the user code with API access"""
        try:
            # Create a safe execution environment
            safe_globals = {
                '__builtins__': {
                    'range': range,
                    'len': len,
                    'abs': abs,
                    'min': min,
                    'max': max,
                    'print': self.api.quick_print,
                    'True': True,
                    'False': False,
                    'None': None,
                },
                # API functions
                'move': self.api.move,
                'get_pos_x': self.api.get_pos_x,
                'get_pos_y': self.api.get_pos_y,
                'get_ground_type': self.api.get_ground_type,
                'get_entity_type': self.api.get_entity_type,
                'can_harvest': self.api.can_harvest,
                'harvest': self.api.harvest,
                'plant': self.api.plant,
                'till': self.api.till,
                'num_items': self.api.num_items,
                'trade': self.api.trade,
                'quick_print': self.api.quick_print,
                'get_time': self.api.get_time,
                # Constants
                'North': North,
                'South': South,
                'East': East,
                'West': West,
                'Entities': Entities,
                'Items': Items,
                'Grounds': Grounds,
                'Unlocks': Unlocks,
            }

            # Execute the code
            exec(code, safe_globals)

        except Exception as e:
            self.api.quick_print(f"Error: {str(e)}")
        finally:
            self.is_running = False

class AssetManager:
    """Manages game assets including textures downloaded from the internet"""

    def __init__(self):
        self.textures = {}
        self.load_assets()

    def load_assets(self):
        """Load or create all game assets"""
        print("Loading game assets...")

        # Create assets directory
        if not os.path.exists("assets"):
            os.makedirs("assets")
            os.makedirs("assets/sprites")

        # Try to download real sprites
        self.download_sprites()

        # Load or create fallback sprites
        self.create_sprites()

        print("Assets loaded!")

    def download_sprites(self):
        """Download real pixel art sprites from the internet"""
        print("Downloading pixel art sprites...")

        # URLs for free pixel art sprites
        sprite_urls = {
            'grass': 'https://opengameart.org/sites/default/files/grass_tile_32x32.png',
            'soil': 'https://opengameart.org/sites/default/files/dirt_tile_32x32.png',
            'carrot': 'https://opengameart.org/sites/default/files/carrot_32x32.png',
            'pumpkin': 'https://opengameart.org/sites/default/files/pumpkin_32x32.png',
            'sunflower': 'https://opengameart.org/sites/default/files/sunflower_32x32.png',
            'tree': 'https://opengameart.org/sites/default/files/tree_32x32.png',
            'drone': 'https://opengameart.org/sites/default/files/robot_32x32.png',
        }

        # Try to download each sprite
        for name, url in sprite_urls.items():
            try:
                print(f"Downloading {name}...")
                import urllib.request
                urllib.request.urlretrieve(url, f"assets/sprites/{name}.png")
                print(f"Downloaded {name}")
            except Exception as e:
                print(f"Could not download {name}: {e}")

    def create_sprites(self):
        """Create or load sprites"""
        # Try to load downloaded sprites first, fallback to procedural
        sprite_names = ['grass', 'soil', 'carrot', 'pumpkin', 'sunflower', 'tree', 'drone']

        for name in sprite_names:
            sprite_path = f"assets/sprites/{name}.png"
            if os.path.exists(sprite_path):
                try:
                    # Load real sprite
                    sprite = pygame.image.load(sprite_path).convert_alpha()
                    sprite = pygame.transform.scale(sprite, (GRID_SIZE, GRID_SIZE))
                    self.textures[name] = sprite
                    print(f"Loaded sprite: {name}")
                except Exception as e:
                    print(f"Could not load {name}: {e}")
                    self.textures[name] = self.create_fallback_sprite(name)
            else:
                # Create procedural sprite
                self.textures[name] = self.create_fallback_sprite(name)

    def create_fallback_sprite(self, name: str) -> pygame.Surface:
        """Create a procedural sprite as fallback"""
        surface = pygame.Surface((GRID_SIZE, GRID_SIZE))
        surface.fill(WHITE)

        # Define colors for different sprites
        colors = {
            'grass': GREEN,
            'soil': BROWN,
            'carrot': ORANGE,
            'pumpkin': ORANGE,
            'sunflower': YELLOW,
            'tree': (34, 139, 34),  # Forest green
            'drone': BLUE,
        }

        color = colors.get(name, GRAY)

        # Create simple shapes
        if name == 'grass':
            # Grass texture
            surface.fill(GREEN)
            for i in range(8):
                x = random.randint(0, GRID_SIZE-1)
                y = random.randint(0, GRID_SIZE-1)
                pygame.draw.circle(surface, (0, 200, 0), (x, y), 2)

        elif name == 'soil':
            # Soil texture
            surface.fill(BROWN)
            for i in range(12):
                x = random.randint(0, GRID_SIZE-1)
                y = random.randint(0, GRID_SIZE-1)
                pygame.draw.circle(surface, (101, 67, 33), (x, y), 1)

        elif name == 'carrot':
            # Carrot shape
            surface.fill((0, 0, 0, 0))  # Transparent
            pygame.draw.polygon(surface, ORANGE, [(16, 8), (12, 24), (20, 24)])
            pygame.draw.rect(surface, GREEN, (14, 4, 4, 8))

        elif name == 'pumpkin':
            # Pumpkin shape
            surface.fill((0, 0, 0, 0))  # Transparent
            pygame.draw.circle(surface, ORANGE, (16, 18), 12)
            pygame.draw.rect(surface, GREEN, (14, 6, 4, 6))

        elif name == 'sunflower':
            # Sunflower shape
            surface.fill((0, 0, 0, 0))  # Transparent
            pygame.draw.circle(surface, YELLOW, (16, 16), 10)
            pygame.draw.circle(surface, (139, 69, 19), (16, 16), 4)
            pygame.draw.rect(surface, GREEN, (14, 20, 4, 8))

        elif name == 'tree':
            # Tree shape
            surface.fill((0, 0, 0, 0))  # Transparent
            pygame.draw.circle(surface, GREEN, (16, 12), 10)
            pygame.draw.rect(surface, BROWN, (14, 18, 4, 10))

        elif name == 'drone':
            # Drone shape
            surface.fill((0, 0, 0, 0))  # Transparent
            pygame.draw.rect(surface, BLUE, (8, 8, 16, 16))
            pygame.draw.circle(surface, WHITE, (12, 12), 2)
            pygame.draw.circle(surface, WHITE, (20, 12), 2)
            pygame.draw.circle(surface, WHITE, (12, 20), 2)
            pygame.draw.circle(surface, WHITE, (20, 20), 2)

        else:
            surface.fill(color)

        return surface

    def get_texture(self, name: str) -> pygame.Surface:
        """Get a texture by name"""
        return self.textures.get(name, self.textures.get('grass'))

class GameRenderer:
    """Handles all game rendering"""

    def __init__(self, screen, asset_manager: AssetManager):
        self.screen = screen
        self.asset_manager = asset_manager
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        self.large_font = pygame.font.Font(None, 32)

    def render_farm(self, game_state: GameState):
        """Render the farm grid with textures"""
        farm_start_x = 50
        farm_start_y = 50

        for y in range(game_state.farm_size):
            for x in range(game_state.farm_size):
                tile = game_state.farm[y][x]
                pos = (farm_start_x + x * GRID_SIZE, farm_start_y + y * GRID_SIZE)

                # Draw ground texture
                if tile.ground_type == GroundType.SOIL:
                    ground_texture = self.asset_manager.get_texture('soil')
                else:
                    ground_texture = self.asset_manager.get_texture('grass')

                self.screen.blit(ground_texture, pos)

                # Draw entity texture
                if tile.entity_type != EntityType.NONE:
                    entity_texture = self._get_entity_texture(tile.entity_type)

                    # Apply growth scaling
                    if tile.entity_growth < 1.0:
                        # Scale texture based on growth
                        scale_factor = 0.3 + 0.7 * tile.entity_growth
                        scaled_size = int(GRID_SIZE * scale_factor)
                        scaled_texture = pygame.transform.scale(entity_texture, (scaled_size, scaled_size))

                        # Center the scaled texture
                        offset_x = (GRID_SIZE - scaled_size) // 2
                        offset_y = (GRID_SIZE - scaled_size) // 2
                        entity_pos = (pos[0] + offset_x, pos[1] + offset_y)

                        self.screen.blit(scaled_texture, entity_pos)
                    else:
                        # Full grown - normal size
                        self.screen.blit(entity_texture, pos)

                # Draw grid lines
                rect = pygame.Rect(pos[0], pos[1], GRID_SIZE, GRID_SIZE)
                pygame.draw.rect(self.screen, (0, 0, 0, 50), rect, 1)

        # Draw drone with texture
        drone_pos = (
            farm_start_x + game_state.drone_x * GRID_SIZE,
            farm_start_y + game_state.drone_y * GRID_SIZE
        )
        drone_texture = self.asset_manager.get_texture('drone')
        self.screen.blit(drone_texture, drone_pos)

        # Add drone glow effect
        drone_rect = pygame.Rect(drone_pos[0], drone_pos[1], GRID_SIZE, GRID_SIZE)
        pygame.draw.rect(self.screen, (0, 255, 255, 100), drone_rect, 2)

    def _get_entity_texture(self, entity_type: EntityType) -> pygame.Surface:
        """Get texture for entity type"""
        texture_map = {
            EntityType.GRASS: 'grass',
            EntityType.CARROT: 'carrot',
            EntityType.PUMPKIN: 'pumpkin',
            EntityType.SUNFLOWER: 'sunflower',
            EntityType.TREE: 'tree',
            EntityType.BUSH: 'grass',  # Use grass texture for bush
        }

        texture_name = texture_map.get(entity_type, 'grass')
        return self.asset_manager.get_texture(texture_name)

    def render_inventory(self, game_state: GameState):
        """Render the inventory panel with better styling"""
        panel_x = 400
        panel_y = 50
        panel_width = 300
        panel_height = 400

        # Background
        pygame.draw.rect(self.screen, (60, 60, 80),
                        (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(self.screen, WHITE,
                        (panel_x, panel_y, panel_width, panel_height), 2)

        # Title bar
        title_rect = pygame.Rect(panel_x, panel_y, panel_width, 30)
        pygame.draw.rect(self.screen, (80, 80, 100), title_rect)
        title = self.font.render("Inventory & Stats", True, WHITE)
        self.screen.blit(title, (panel_x + 10, panel_y + 5))

        # Game stats
        y_offset = 40
        stats = [
            f"Time: {game_state.game_time:.1f}s",
            f"Drone: ({game_state.drone_x}, {game_state.drone_y})",
            f"Farm Size: {game_state.farm_size}x{game_state.farm_size}",
            "",
            "Resources:"
        ]

        for stat in stats:
            if stat:
                color = YELLOW if stat.startswith("Time") else WHITE
                text = self.small_font.render(stat, True, color)
                self.screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 18

        # Items with icons
        for item, count in game_state.inventory.items():
            if count > 0:
                # Item icon (simple colored square)
                icon_color = self._get_item_color(item)
                icon_rect = pygame.Rect(panel_x + 15, panel_y + y_offset + 2, 12, 12)
                pygame.draw.rect(self.screen, icon_color, icon_rect)
                pygame.draw.rect(self.screen, WHITE, icon_rect, 1)

                # Item text
                item_name = item.value.replace('_', ' ')
                text = self.small_font.render(f"{item_name}: {count}", True, WHITE)
                self.screen.blit(text, (panel_x + 35, panel_y + y_offset))
                y_offset += 18

    def _get_item_color(self, item: ItemType) -> Tuple[int, int, int]:
        """Get color for item icons"""
        colors = {
            ItemType.CARROT: ORANGE,
            ItemType.CARROT_SEED: (255, 140, 0),
            ItemType.PUMPKIN: ORANGE,
            ItemType.PUMPKIN_SEED: (255, 165, 0),
            ItemType.SUNFLOWER_SEED: YELLOW,
            ItemType.WOOD: BROWN,
            ItemType.HAY: (255, 255, 0),
            ItemType.WATER_TANK: BLUE,
            ItemType.FERTILIZER: GREEN,
            ItemType.GOLD: (255, 215, 0),
        }
        return colors.get(item, GRAY)

    def render_code_editor(self, code: str, cursor_pos: int):
        """Render the code editor with syntax highlighting"""
        editor_x = 750
        editor_y = 50
        editor_width = 600
        editor_height = 400

        # Background
        pygame.draw.rect(self.screen, (40, 40, 40),
                        (editor_x, editor_y, editor_width, editor_height))
        pygame.draw.rect(self.screen, WHITE,
                        (editor_x, editor_y, editor_width, editor_height), 2)

        # Title bar
        title_rect = pygame.Rect(editor_x, editor_y, editor_width, 30)
        pygame.draw.rect(self.screen, (60, 60, 60), title_rect)
        title = self.font.render("Code Editor - Press F5 to Execute", True, WHITE)
        self.screen.blit(title, (editor_x + 10, editor_y + 5))

        # Code area
        code_area = pygame.Rect(editor_x + 5, editor_y + 35, editor_width - 10, editor_height - 40)
        pygame.draw.rect(self.screen, (30, 30, 30), code_area)

        # Line numbers background
        line_num_width = 40
        line_num_area = pygame.Rect(editor_x + 5, editor_y + 35, line_num_width, editor_height - 40)
        pygame.draw.rect(self.screen, (50, 50, 50), line_num_area)

        # Code lines with syntax highlighting
        lines = code.split('\n')
        y_offset = 40
        line_height = 18

        for i, line in enumerate(lines):
            if y_offset > editor_height - 25:
                break

            # Line number
            line_num = self.small_font.render(f"{i+1:2d}", True, GRAY)
            self.screen.blit(line_num, (editor_x + 8, editor_y + y_offset))

            # Syntax highlighted code
            self._render_syntax_highlighted_line(line, editor_x + 50, editor_y + y_offset)
            y_offset += line_height

        # Cursor (simple blinking effect)
        if int(time.time() * 2) % 2:  # Blink every 0.5 seconds
            cursor_x = editor_x + 50 + len(code.split('\n')[-1]) * 7
            cursor_y = editor_y + 40 + (len(code.split('\n')) - 1) * line_height
            if cursor_y < editor_y + editor_height - 25:
                pygame.draw.line(self.screen, WHITE,
                               (cursor_x, cursor_y), (cursor_x, cursor_y + 16), 2)

    def _render_syntax_highlighted_line(self, line: str, x: int, y: int):
        """Render a line with basic syntax highlighting"""
        # Keywords
        keywords = ['def', 'if', 'else', 'elif', 'for', 'while', 'in', 'and', 'or', 'not', 'True', 'False', 'None']
        functions = ['move', 'plant', 'harvest', 'till', 'get_pos_x', 'get_pos_y', 'quick_print', 'num_items', 'trade']

        words = line.split()
        current_x = x

        for word in words:
            # Determine color
            if word in keywords:
                color = (255, 100, 100)  # Red for keywords
            elif word.rstrip('()') in functions:
                color = (100, 255, 100)  # Green for functions
            elif word.startswith('#'):
                color = GRAY  # Gray for comments
            elif word.startswith('"') or word.startswith("'"):
                color = (255, 255, 100)  # Yellow for strings
            else:
                color = WHITE  # White for normal text

            text = self.small_font.render(word + ' ', True, color)
            self.screen.blit(text, (current_x, y))
            current_x += text.get_width()

    def render_output(self, output_log: List[str]):
        """Render the output log with better styling"""
        output_x = 750
        output_y = 470
        output_width = 600
        output_height = 200

        # Background
        pygame.draw.rect(self.screen, (20, 20, 20),
                        (output_x, output_y, output_width, output_height))
        pygame.draw.rect(self.screen, WHITE,
                        (output_x, output_y, output_width, output_height), 2)

        # Title bar
        title_rect = pygame.Rect(output_x, output_y, output_width, 25)
        pygame.draw.rect(self.screen, (40, 40, 40), title_rect)
        title = self.font.render("Console Output", True, WHITE)
        self.screen.blit(title, (output_x + 10, output_y + 3))

        # Clear button area
        clear_rect = pygame.Rect(output_x + output_width - 60, output_y + 2, 55, 21)
        pygame.draw.rect(self.screen, (80, 40, 40), clear_rect)
        clear_text = self.small_font.render("Clear", True, WHITE)
        self.screen.blit(clear_text, (output_x + output_width - 50, output_y + 5))

        # Output lines (show last 9 lines to fit better)
        y_offset = 30
        line_height = 16
        max_lines = (output_height - 35) // line_height

        recent_logs = output_log[-max_lines:] if len(output_log) > max_lines else output_log

        for line in recent_logs:
            if y_offset > output_height - 20:
                break

            # Color code different types of messages
            color = WHITE
            if "Error:" in line:
                color = (255, 100, 100)  # Red for errors
            elif "Warning:" in line:
                color = (255, 255, 100)  # Yellow for warnings
            elif line.startswith("["):
                color = (150, 150, 255)  # Light blue for timestamped messages

            # Truncate long lines
            if len(line) > 70:
                line = line[:67] + "..."

            text = self.small_font.render(line, True, color)
            self.screen.blit(text, (output_x + 10, output_y + y_offset))
            y_offset += line_height

    def render_controls(self, is_executing: bool):
        """Render control instructions and status"""
        # Control panel
        panel_x = 50
        panel_y = 470
        panel_width = 300
        panel_height = 200

        pygame.draw.rect(self.screen, (40, 60, 40),
                        (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(self.screen, WHITE,
                        (panel_x, panel_y, panel_width, panel_height), 2)

        # Title
        title_rect = pygame.Rect(panel_x, panel_y, panel_width, 25)
        pygame.draw.rect(self.screen, (60, 80, 60), title_rect)
        title = self.font.render("Controls & Status", True, WHITE)
        self.screen.blit(title, (panel_x + 10, panel_y + 3))

        # Execution status
        y_offset = 35
        if is_executing:
            status_text = "🤖 DRONE EXECUTING CODE..."
            status_color = GREEN
        else:
            status_text = "⏸️ Drone idle - Ready for commands"
            status_color = YELLOW

        status = self.small_font.render(status_text, True, status_color)
        self.screen.blit(status, (panel_x + 10, panel_y + y_offset))
        y_offset += 25

        # Controls
        controls = [
            ("F5", "Execute Code", GREEN),
            ("F6", "Stop Execution", RED),
            ("DEL", "Clear Code", ORANGE),
            ("ESC", "Quit Game", GRAY),
            ("", "", WHITE),
            ("API Functions:", "", YELLOW),
            ("move(North/South/East/West)", "", LIGHT_GRAY),
            ("plant(Entities.Carrot)", "", LIGHT_GRAY),
            ("harvest(), till()", "", LIGHT_GRAY),
            ("quick_print('message')", "", LIGHT_GRAY),
        ]

        for key, desc, color in controls:
            if key and desc:
                key_text = self.small_font.render(f"{key}:", True, WHITE)
                desc_text = self.small_font.render(desc, True, color)
                self.screen.blit(key_text, (panel_x + 10, panel_y + y_offset))
                self.screen.blit(desc_text, (panel_x + 50, panel_y + y_offset))
            elif desc:
                text = self.small_font.render(desc, True, color)
                self.screen.blit(text, (panel_x + 10, panel_y + y_offset))
            y_offset += 16

    def _get_entity_color(self, entity_type: EntityType, growth: float) -> Tuple[int, int, int]:
        """Get color for entity based on type and growth"""
        base_colors = {
            EntityType.GRASS: GREEN,
            EntityType.CARROT: ORANGE,
            EntityType.PUMPKIN: ORANGE,
            EntityType.SUNFLOWER: YELLOW,
            EntityType.TREE: BROWN,
            EntityType.BUSH: GREEN,
        }

        base_color = base_colors.get(entity_type, GRAY)

        # Darken color if not fully grown
        if growth < 1.0:
            factor = 0.3 + 0.7 * growth
            return tuple(int(c * factor) for c in base_color)

        return base_color

class GameSimulation:
    """Handles game simulation and growth mechanics"""

    def __init__(self, game_state: GameState):
        self.game_state = game_state
        self.last_update = time.time()

    def update(self, dt: float):
        """Update game simulation"""
        self.game_state.game_time += dt

        # Update plant growth
        for y in range(self.game_state.farm_size):
            for x in range(self.game_state.farm_size):
                tile = self.game_state.farm[y][x]

                if tile.entity_type != EntityType.NONE and tile.entity_growth < 1.0:
                    # Growth rate depends on entity type and conditions
                    growth_rate = self._get_growth_rate(tile.entity_type, tile)
                    tile.entity_growth = min(1.0, tile.entity_growth + growth_rate * dt)

    def _get_growth_rate(self, entity_type: EntityType, tile: FarmTile) -> float:
        """Get growth rate for an entity"""
        base_rates = {
            EntityType.GRASS: 0.5,  # Fast growing
            EntityType.CARROT: 0.2,  # Medium
            EntityType.PUMPKIN: 0.1,  # Slow
            EntityType.SUNFLOWER: 0.15,  # Medium-slow
            EntityType.TREE: 0.05,  # Very slow
            EntityType.BUSH: 0.3,  # Medium-fast
        }

        base_rate = base_rates.get(entity_type, 0.1)

        # Bonus for tilled soil
        if tile.ground_type == GroundType.SOIL:
            base_rate *= 1.5

        # Water level affects growth
        base_rate *= (0.5 + tile.water_level * 0.5)

        return base_rate

class FarmerReplacedGame:
    """Main game class"""

    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("The Farmer Was Replaced - Python Clone")
        self.clock = pygame.time.Clock()

        # Initialize asset manager first
        self.asset_manager = AssetManager()

        self.game_state = GameState()
        self.code_executor = CodeExecutor(self.game_state)
        self.renderer = GameRenderer(self.screen, self.asset_manager)
        self.simulation = GameSimulation(self.game_state)

        # Code editor state
        self.code = self._get_default_code()
        self.cursor_pos = 0

        # Game state
        self.running = True
        self.last_time = time.time()

    def _get_default_code(self) -> str:
        """Get default starter code"""
        return """# Welcome to The Farmer Was Replaced - Python Clone!
# Write Python code to control your farming drone
# Press F5 to execute, F6 to stop, DEL to clear

# Basic movement and farming
quick_print("Starting automated farming...")

# Plant carrots in a row
for i in range(5):
    move(East)
    if get_ground_type() != Grounds.Soil:
        till()  # Prepare soil
    if get_entity_type() == None:
        plant(Entities.Carrot)
        quick_print("Planted carrot at", get_pos_x(), get_pos_y())

# Return to start
for i in range(5):
    move(West)

# Wait and harvest loop
quick_print("Waiting for crops to grow...")
while True:
    harvested = False
    # Check all positions
    for y in range(5):
        for x in range(5):
            if can_harvest():
                harvest()
                quick_print("Harvested at", get_pos_x(), get_pos_y())
                harvested = True
            move(East)
        move(South)
        for x in range(5):
            move(West)
        move(North)

    if harvested:
        quick_print("Harvest complete! Gold:", num_items(Items.Gold))
        break

# Buy more seeds if we have gold
if num_items(Items.Gold) >= 50:
    trade(Items.Carrot_Seed, 5)
    quick_print("Bought more seeds!")
"""

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False

                elif event.key == pygame.K_F5:
                    # Execute code
                    self.code_executor.execute_code(self.code)

                elif event.key == pygame.K_F6:
                    # Stop execution
                    self.code_executor.stop()

                elif event.key == pygame.K_BACKSPACE:
                    if self.code:
                        self.code = self.code[:-1]

                elif event.key == pygame.K_RETURN:
                    self.code += '\n'

                elif event.key == pygame.K_TAB:
                    self.code += '    '  # 4 spaces for indentation

                elif event.key == pygame.K_DELETE:
                    # Clear all code
                    self.code = ""

                elif event.key == pygame.K_HOME:
                    # Go to start of code
                    pass  # Could implement cursor movement

                else:
                    # Add character to code - improved handling
                    if hasattr(event, 'unicode') and event.unicode:
                        if event.unicode.isprintable() or event.unicode in [' ', '\t']:
                            self.code += event.unicode
                    elif event.key == pygame.K_SPACE:
                        self.code += ' '

    def update(self):
        """Update game state"""
        current_time = time.time()
        dt = current_time - self.last_time
        self.last_time = current_time

        # Update simulation
        self.simulation.update(dt)

    def render(self):
        """Render the game"""
        # Background gradient
        for y in range(SCREEN_HEIGHT):
            color_factor = y / SCREEN_HEIGHT
            color = (
                int(20 + color_factor * 10),
                int(25 + color_factor * 15),
                int(35 + color_factor * 20)
            )
            pygame.draw.line(self.screen, color, (0, y), (SCREEN_WIDTH, y))

        # Title
        title_text = self.renderer.large_font.render("The Farmer Was Replaced - Python Clone", True, WHITE)
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, 25))
        self.screen.blit(title_text, title_rect)

        # Render all components
        self.renderer.render_farm(self.game_state)
        self.renderer.render_inventory(self.game_state)
        self.renderer.render_code_editor(self.code, self.cursor_pos)
        self.renderer.render_output(self.code_executor.api.output_log)
        self.renderer.render_controls(self.code_executor.is_running)

        pygame.display.flip()

    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.render()
            self.clock.tick(FPS)

        # Clean up
        self.code_executor.stop()
        pygame.quit()
        sys.exit()

def main():
    """Main entry point"""
    game = FarmerReplacedGame()
    game.run()

if __name__ == "__main__":
    main()
