# The Farmer Was Replaced - Python Clone

A Python implementation inspired by the original game "The Farmer Was Replaced" by <PERSON><PERSON>. This is a programming-based farming automation game where you write Python-like code to control a drone that manages your farm automatically.

## Game Overview

Program a drone using Python code to automate farming tasks. Watch your drone execute your code in real-time as it plants, harvests, and manages resources on a grid-based farm. The game teaches programming concepts while providing the satisfaction of automation.

## Features Implemented

### Core Mechanics
- **Grid-based farm** (5x5 starting size, expandable)
- **Drone movement** with wrapping edges
- **Programming interface** using real Python syntax
- **Real-time code execution** in separate thread
- **Resource management** and inventory system
- **Plant growth simulation** with visual progress

### Programming API
The game provides a Python API similar to the original:

#### Movement
- `move(direction)` - Move drone (North, South, East, West)
- `get_pos_x()`, `get_pos_y()` - Get current position

#### Farm Management
- `till()` - Till ground to create soil
- `plant(entity)` - Plant seeds (Entities.Carrot, Entities.Pumpkin, etc.)
- `harvest()` - Harvest mature plants
- `can_harvest()` - Check if plant is ready

#### Information
- `get_ground_type()` - Check ground type (Soil/Grassland)
- `get_entity_type()` - Check what's planted
- `num_items(item)` - Check inventory count
- `get_time()` - Get execution time

#### Trading & Resources
- `trade(item, quantity)` - Buy items with gold
- `quick_print(*args)` - Output to log

### Available Entities
- **Carrot** - Fast growing, basic crop
- **Pumpkin** - Slow growing, valuable
- **Sunflower** - Medium growth, decorative
- **Tree** - Very slow, produces wood
- **Bush** - Fast growing, produces berries
- **Grass** - Wild growth, produces hay

### Items & Resources
- Seeds: Carrot_Seed, Pumpkin_Seed, Sunflower_Seed
- Products: Carrot, Pumpkin, Wood, Hay
- Tools: Water_Tank, Fertilizer
- Currency: Gold

## Controls

- **F5** - Execute your code
- **F6** - Stop code execution
- **ESC** - Quit game
- **Type** - Edit code in the editor
- **Backspace** - Delete characters
- **Enter** - New line
- **Tab** - Add indentation (4 spaces)

## Example Code

```python
# Basic farming loop
for i in range(5):
    move(East)
    if get_ground_type() != Grounds.Soil:
        till()
    if get_entity_type() == None:
        plant(Entities.Carrot)

# Harvest when ready
for y in range(5):
    for x in range(5):
        if can_harvest():
            harvest()
            quick_print("Harvested at", get_pos_x(), get_pos_y())
        move(East)
    move(South)
    for x in range(5):
        move(West)

# Check resources
quick_print("Gold:", num_items(Items.Gold))
quick_print("Carrots:", num_items(Items.Carrot))

# Buy more seeds if needed
if num_items(Items.Carrot_Seed) < 10:
    trade(Items.Carrot_Seed, 5)
```

## Installation & Running

### Requirements
- Python 3.7+
- Pygame

### Install Dependencies
```bash
pip install pygame
```

### Run the Game
```bash
python farmer_replaced_clone.py
```

## Game Mechanics

### Growth System
- Plants grow over time from 0% to 100%
- Growth rate depends on:
  - Plant type (Grass fastest, Trees slowest)
  - Ground type (Soil grows 50% faster than Grassland)
  - Water level (affects growth speed)

### Trading System
- Use Gold to buy seeds and tools
- Prices:
  - Carrot_Seed: 10 Gold
  - Pumpkin_Seed: 25 Gold
  - Sunflower_Seed: 15 Gold
  - Fertilizer: 50 Gold
  - Water_Tank: 30 Gold

### Farm Layout
- 5x5 grid starting size
- Drone starts at position (0,0)
- Movement wraps around edges
- Two ground types: Grassland (green) and Soil (brown)

## Comparison with Original

### Implemented Features ✅
- Python-like programming language
- Grid-based farm with drone
- Plant/harvest/till mechanics
- Resource management
- Real-time code execution
- Growth simulation
- Trading system
- Multiple crop types

### Simplified Features ⚠️
- Smaller starting farm (5x5 vs larger)
- Simplified unlock system
- Basic graphics (colored rectangles vs detailed sprites)
- Limited crop variety
- No companion planting mechanics
- No advanced optimization challenges

### Missing Features ❌
- Research tree with unlocks
- Leaderboards and timed runs
- Advanced plant mechanics (sunflower petals, etc.)
- Maze generation
- Complex automation challenges
- Save/load functionality
- Multiple farm sizes

## Technical Implementation

### Architecture
- **GameState**: Manages farm, inventory, and game data
- **DroneAPI**: Provides programming interface
- **CodeExecutor**: Safely executes user code in separate thread
- **GameRenderer**: Handles all visual rendering
- **GameSimulation**: Updates plant growth and game mechanics

### Safety Features
- Code execution in restricted environment
- Thread-safe execution with stop capability
- Error handling and logging
- Limited built-in functions to prevent system access

## Educational Value

This game teaches:
- **Programming Fundamentals**: Loops, conditionals, functions
- **Algorithm Design**: Efficient farming strategies
- **Resource Management**: Balancing costs and production
- **Automation Concepts**: Writing code to handle repetitive tasks
- **Problem Solving**: Optimizing for different objectives

## License

This is an educational project inspired by "The Farmer Was Replaced" by Timon Herzog. 
Original game: https://store.steampowered.com/app/2060160/The_Farmer_Was_Replaced/

## Screenshots & Visual Style

The game features a clean, functional interface with:
- **Farm Grid**: 5x5 grid with textured tiles (grass/soil) and growing crops
- **Code Editor**: Syntax-highlighted Python editor with line numbers
- **Console Output**: Real-time feedback from your drone's actions
- **Inventory Panel**: Resource tracking and game statistics
- **Controls Panel**: Help and execution status

## Known Issues & Limitations

1. **Texture Downloads**: Some texture URLs may return 404 errors - game uses fallback procedural graphics
2. **Code Editor**: Basic text editing (no advanced IDE features like autocomplete)
3. **Performance**: Large loops may cause temporary UI freezing
4. **Save System**: No persistent save/load functionality yet

## Troubleshooting

### Game Won't Start
- Ensure Python 3.7+ is installed
- Install pygame: `pip install pygame`
- Check that all files are in the same directory

### Code Not Executing
- Press F5 to execute code
- Check console output for error messages
- Ensure proper Python syntax (indentation matters!)

### Typing Issues
- Use DEL key to clear all code
- Backspace to delete characters
- Tab for indentation (4 spaces)

## Advanced Examples

See `example_farming_script.py` for complete farming automation examples including:
- Basic linear farming
- Grid-based farming
- Continuous harvest/replant loops
- Mixed crop strategies
- Resource optimization algorithms

## Performance Tips

1. **Avoid Infinite Loops**: Always include break conditions
2. **Use quick_print Sparingly**: Too much output can slow execution
3. **Optimize Movement**: Plan efficient paths to minimize drone movement
4. **Resource Management**: Check inventory before trading or planting

## Educational Applications

This game is excellent for teaching:
- **Basic Programming**: Variables, loops, conditionals
- **Algorithm Design**: Pathfinding, optimization
- **Resource Management**: Economic decision making
- **Automation Concepts**: Real-world programming applications
- **Problem Solving**: Breaking complex tasks into simple steps

## Future Development Ideas

1. **Enhanced Graphics**: Better sprites and animations
2. **More Crops**: Additional plant types with unique mechanics
3. **Research Tree**: Unlock system for new capabilities
4. **Challenges**: Specific objectives and time limits
5. **Multiplayer**: Compare automation solutions
6. **Save System**: Persistent progress
7. **Advanced API**: More drone capabilities and sensors

## Contributing

To contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

Areas needing improvement:
- Better texture assets
- Enhanced UI/UX
- Performance optimizations
- Additional game mechanics
- Bug fixes

## Credits

- **Original Game**: "The Farmer Was Replaced" by Timon Herzog
- **Inspiration**: Steam page and community discussions
- **Graphics**: Procedural generation with fallback system
- **Implementation**: Python with Pygame

## License

This is an educational project inspired by the original game.
Not affiliated with the original developers.
Use for learning and educational purposes.

---

**🎮 Ready to start programming your farm? Launch the game and press F5 to execute your first automation script!**

Enjoy programming your automated farm! 🚜🤖
