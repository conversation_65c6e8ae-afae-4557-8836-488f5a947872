# Example Farming Scripts for "The Farmer Was Replaced" Clone
# Copy and paste these into the game's code editor and press F5 to execute

# SCRIPT 1: Basic Carrot Farming
"""
# Simple carrot farming in a line
quick_print("Starting basic carrot farming...")

# Plant carrots in a row
for i in range(5):
    move(East)
    if get_ground_type() != Grounds.Soil:
        till()
    if get_entity_type() == None:
        plant(Entities.Carrot)
        quick_print("Planted carrot at", get_pos_x(), get_pos_y())

# Return to start
for i in range(5):
    move(West)

quick_print("Planting complete! Seeds left:", num_items(Items.Carrot_Seed))
"""

# SCRIPT 2: Advanced Grid Farming
"""
# Farm the entire 5x5 grid efficiently
quick_print("Starting advanced grid farming...")

# Plant phase
for y in range(5):
    for x in range(5):
        # Till and plant if empty
        if get_ground_type() != Grounds.Soil:
            till()
        if get_entity_type() == None and num_items(Items.Carrot_Seed) > 0:
            plant(Entities.Carrot)
        
        # Move to next position
        if x < 4:  # Not at end of row
            move(East)
    
    # Move to next row
    if y < 4:  # Not at last row
        move(South)
        for x in range(4):
            move(West)

quick_print("Grid planting complete!")
"""

# SCRIPT 3: Harvest and Replant Loop
"""
# Continuous farming loop
quick_print("Starting continuous farming loop...")

planted_count = 0
harvested_count = 0

while True:
    # Scan the entire farm
    for y in range(5):
        for x in range(5):
            # Harvest if ready
            if can_harvest():
                harvest()
                harvested_count += 1
                quick_print("Harvested! Total:", harvested_count)
            
            # Plant if empty and have seeds
            elif get_entity_type() == None and num_items(Items.Carrot_Seed) > 0:
                if get_ground_type() != Grounds.Soil:
                    till()
                plant(Entities.Carrot)
                planted_count += 1
            
            # Move to next position
            if x < 4:
                move(East)
        
        # Move to next row
        if y < 4:
            move(South)
            for x in range(4):
                move(West)
    
    # Check if we should buy more seeds
    if num_items(Items.Gold) >= 50 and num_items(Items.Carrot_Seed) < 5:
        trade(Items.Carrot_Seed, 5)
        quick_print("Bought more seeds! Gold left:", num_items(Items.Gold))
    
    # Status update
    if planted_count > 0 or harvested_count > 0:
        quick_print("Status - Planted:", planted_count, "Harvested:", harvested_count)
        quick_print("Resources - Seeds:", num_items(Items.Carrot_Seed), "Gold:", num_items(Items.Gold))
    
    # Break if we have no seeds and no gold
    if num_items(Items.Carrot_Seed) == 0 and num_items(Items.Gold) < 10:
        quick_print("Out of resources! Stopping.")
        break
"""

# SCRIPT 4: Mixed Crop Farming
"""
# Plant different crops in different areas
quick_print("Starting mixed crop farming...")

# Plant carrots in top rows
for y in range(2):
    for x in range(5):
        if get_ground_type() != Grounds.Soil:
            till()
        if get_entity_type() == None and num_items(Items.Carrot_Seed) > 0:
            plant(Entities.Carrot)
            quick_print("Planted carrot at", get_pos_x(), get_pos_y())
        
        if x < 4:
            move(East)
    
    if y < 1:
        move(South)
        for x in range(4):
            move(West)

# Move to middle area for pumpkins
move(South)
for x in range(4):
    move(West)

# Buy pumpkin seeds if we have gold
if num_items(Items.Gold) >= 25:
    trade(Items.Pumpkin_Seed, 1)
    quick_print("Bought pumpkin seed!")

# Plant pumpkins in middle rows
for y in range(2):
    for x in range(5):
        if get_ground_type() != Grounds.Soil:
            till()
        if get_entity_type() == None and num_items(Items.Pumpkin_Seed) > 0:
            plant(Entities.Pumpkin)
            quick_print("Planted pumpkin at", get_pos_x(), get_pos_y())
        
        if x < 4:
            move(East)
    
    if y < 1:
        move(South)
        for x in range(4):
            move(West)

quick_print("Mixed farming setup complete!")
quick_print("Carrots grow fast, pumpkins grow slow but are valuable")
"""

# SCRIPT 5: Efficient Resource Management
"""
# Smart farming with resource optimization
quick_print("Starting smart resource management...")

def scan_and_harvest():
    harvested = 0
    for y in range(5):
        for x in range(5):
            if can_harvest():
                harvest()
                harvested += 1
            if x < 4:
                move(East)
        if y < 4:
            move(South)
            for x in range(4):
                move(West)
    return harvested

def plant_efficiently():
    planted = 0
    for y in range(5):
        for x in range(5):
            if get_entity_type() == None:
                if get_ground_type() != Grounds.Soil:
                    till()
                
                # Choose best crop based on resources
                if num_items(Items.Pumpkin_Seed) > 0 and num_items(Items.Carrot_Seed) > 10:
                    plant(Entities.Pumpkin)  # Plant valuable pumpkins when we have backup carrots
                    planted += 1
                elif num_items(Items.Carrot_Seed) > 0:
                    plant(Entities.Carrot)
                    planted += 1
            
            if x < 4:
                move(East)
        if y < 4:
            move(South)
            for x in range(4):
                move(West)
    return planted

# Main farming loop
cycle = 0
while cycle < 10:  # Run for 10 cycles
    cycle += 1
    quick_print("=== Cycle", cycle, "===")
    
    # Harvest first
    harvested = scan_and_harvest()
    if harvested > 0:
        quick_print("Harvested", harvested, "crops")
    
    # Buy seeds if profitable
    gold = num_items(Items.Gold)
    if gold >= 50:
        trade(Items.Carrot_Seed, 3)
        quick_print("Bought carrot seeds")
    if gold >= 75:
        trade(Items.Pumpkin_Seed, 1)
        quick_print("Bought pumpkin seed")
    
    # Plant crops
    planted = plant_efficiently()
    if planted > 0:
        quick_print("Planted", planted, "new crops")
    
    # Status report
    quick_print("Resources:", "Gold:", num_items(Items.Gold), 
               "Carrot Seeds:", num_items(Items.Carrot_Seed),
               "Pumpkin Seeds:", num_items(Items.Pumpkin_Seed))

quick_print("Smart farming complete!")
"""

print("Example scripts ready! Copy any script above into the game editor and press F5 to run.")
print("Remember to remove the triple quotes (\"\"\") when copying!")
